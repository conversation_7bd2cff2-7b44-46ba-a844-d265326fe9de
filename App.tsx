import React, { useState, useRef } from 'react';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

// 定义成绩数据类型
interface ScoreData {
  testDate: string;
  studentName: string;
  testNumber: string;
  overallScore: number;
  speaking: number;
  listening: number;
  writing: number;
  reading: number;
}

// 根据分数计算CEFR等级
const getCEFRLevel = (score: number): string => {
  if (score >= 70) return 'C2';
  if (score >= 61) return 'C1';
  if (score >= 46) return 'B2';
  if (score >= 37) return 'B1';
  if (score >= 20) return 'A2';
  if (score >= 10) return 'A1';
  return '<A1';
};

// 根据CEFR等级和技能类型获取标准描述
const getCEFRDescription = (score: number, skill: 'speaking' | 'listening' | 'writing' | 'reading'): string => {
  const level = getCEFRLevel(score);

  const descriptions = {
    A1: {
      speaking: 'Can understand and use familiar, specific expressions and very basic sentences that are relevant to their preferences every day.',
      listening: 'Can introduce or ask questions about personal information about yourself or others, such as where you live, your relationships, and what you own.',
      writing: 'Understand the expressions or statements that are often used in the environment closest to you, such as very basic personal and family information, shopping, regional geography, and employment.',
      reading: 'The ability to communicate with people in simple, routine tasks that usually require only simple, direct daily messages. Additionally, learners at this level are able to describe their own background and things closest to them in the environment in simple words.'
    },
    A2: {
      speaking: 'Be able to understand the expressions or statements that are often used in the environment closest to you.',
      listening: 'Simple and routine work that can be communicated with people, which usually requires simple and direct daily messages.',
      writing: 'Be able to understand the expressions or statements that are often used in the environment closest to you.',
      reading: 'The ability to describe one\'s background and things in the environment closest to oneself in a crude language.'
    },
    B1: {
      speaking: 'Be able to understand the familiar things encountered in the work, study environment, leisure environment and so on.',
      listening: 'You can travel in the areas where the language is spoken and correspond to various possible situations, and you can also provide simple information about things that you are interested in or familiar with.',
      writing: 'Be able to understand the familiar things encountered in the work, study environment, leisure environment and so on.',
      reading: 'It can also describe experiences, events, dreams, wishes, and ambitions, and can give a brief explanation of one\'s opinions or plans.'
    },
    B2: {
      speaking: 'Ability to understand the specific and abstract themes of complex paragraphs, including skillfully discussing one\'s own area of expertise.',
      listening: 'Can interact routinely with native speakers of the language in a natural and fluid manner.',
      writing: 'Ability to understand the specific and abstract themes of complex paragraphs, including skillfully discussing one\'s own area of expertise.',
      reading: 'You can say clear, detailed words about a wide range of topics, and you can explain an issue, pros and cons, or a variety of ideas.'
    },
    C1: {
      speaking: 'Can understand a wide range of messages that are demanding, lengthy, or implicit in meaning.',
      listening: 'There is no obvious word shortage, and the language is used flexibly and efficiently for social, academic and professional purposes.',
      writing: 'Can understand a wide range of messages that are demanding, lengthy, or implicit in meaning.',
      reading: 'The ability to produce clear, well-structured, detailed text on complex topics that demonstrates a flexible organization, connectivity, and clever strategy.'
    },
    C2: {
      speaking: 'Can easily understand any information received and outline and restructure different arguments based on different written or spoken sources.',
      listening: 'The expressions proposed are natural and very fluent, tightly capturing the most vivid part of the language, and can distinguish the subtle meaning of the profession in more complex situations.',
      writing: 'Can easily understand any information received and outline and restructure different arguments based on different written or spoken sources.',
      reading: 'The expressions proposed are natural and very fluent, tightly capturing the most vivid part of the language, and can distinguish the subtle meaning of the profession in more complex situations.'
    }
  };

  const levelDescriptions = descriptions[level as keyof typeof descriptions];
  if (!levelDescriptions) {
    return 'Description not available';
  }
  return levelDescriptions[skill] || 'Description not available';
};

// 根据分数生成描述
const getScoreDescription = (score: number): string => {
  if (score >= 70) {
    return "Close to native language level, accurate understanding of implicit information and skilled debate, suitable for top university graduate admission or highly specialized work.";
  } else if (score >= 61) {
    return "Fluent in language and handling abstract concepts in academic or professional situations, such as writing papers, participating in debates.";
  } else if (score >= 46) {
    return "Ability to understand complex text and participate in professional discussions, but occasional grammar errors. Suitable for pre-college courses or undergraduate applications from non-English speaking countries.";
  } else if (score >= 37) {
    return "Communication can be done with familiar topics (such as routine work or study), but complex grammar or accent may cause barriers.";
  } else if (score >= 20) {
    return "Can understand common phrases and participate in simple conversations (such as talking about family, weather), adapt to short trips or basic living needs.";
  } else if (score >= 10) {
    return "Master simple daily language, such as self-introduction, shopping, but limited in expression.";
  } else {
    return "Extremely limited English proficiency, requiring significant support for basic communication.";
  }
};

// 根据分数获取技能具体评价
const getSkillDescription = (score: number, skill: 'listening' | 'speaking' | 'reading' | 'writing'): string => {
  const descriptions = {
    listening: {
      70: "Can easily understand long, complex and detailed speeches, identify the speaker's unexpressed but implied ideas, attitudes, opinions or purposes. Can understand words in a wide range of fields, including professional, technical and academic terms and idiomatic expressions.",
      61: "Easy to understand listening materials with various accents and speech rates, including professional discussions, and able to capture subtle differences and logical relationships.",
      46: "Can understand complex content such as academic lectures and news reports, grasp details and implied meanings, and adapt to fast speech speed.",
      37: "Can understand conversations about familiar topics (e.g., school, work, travel), can grasp the main information, but complex grammar or accent may cause comprehension difficulties.",
      20: "Can understand simple, slow daily conversations, such as greetings, shopping, asking for directions and other basic scenarios.",
      10: "There is a great difficulty in listening, and may only be able to understand some simple content or key words."
    },
    speaking: {
      70: "The speech is as smooth as flowing clouds, with rigorous logic, good coherence and free use of language skills, showing your excellent ability of expression.",
      61: "The language is natural and fluent, with flexible use of advanced vocabulary and rhetorical devices to adapt to rapid conversation and debate.",
      46: "Can express ideas clearly and coherently, using complex sentence patterns, and adapt to academic or professional discussions.",
      37: "Can participate in discussions on familiar topics and express basic points of view, but has limited language organization ability and needs to stop frequently.",
      20: "Can use simple words and phrases to describe familiar things, but the expression is not fluent, grammar errors are more.",
      10: "Incoherent expression, many grammatical errors, difficult to participate in effective dialogue."
    },
    reading: {
      70: "Can easily understand complex text, including professional, technical and academic articles, grasp the details and implied meaning.",
      61: "Read professional literature easily, understand abstract concepts and implied meanings, and adapt to difficult texts.",
      46: "Can understand complex materials such as academic texts and long reports, analyze the author's point of view and logical structure.",
      37: "Read articles (e.g., news, emails) on familiar topics and grasp the main idea, but complex sentences or unfamiliar words may affect comprehension.",
      20: "Can understand simple short texts (such as advertisements, menus), but need to rely on pictures or contextual cues.",
      10: "Only able to understand some simple content, difficult to deal with complex text."
    },
    writing: {
      70: "The vocabulary is rich, the advanced vocabulary and complex sentence patterns are used freely, the structure of the article is clear, the logic is rigorous and flawless, which perfectly meets the requirements of the topic, showing the profound language skills and excellent writing ability.",
      61: "The language is precise and varied, and can write difficult texts (such as papers and reviews) to demonstrate critical thinking.",
      46: "Ability to write structured academic articles or reports, using complex sentences and conjunctions, and adapting to formal writing requirements.",
      37: "Can write short articles (such as emails, diaries) to express basic ideas, but the richness and coherence of language are insufficient.",
      20: "Can write simple sentences to describe things, but there are many grammatical errors and loose structure.",
      10: "Can only use the words from the nearest book to form a sentence, with many grammatical errors and incomplete structure."
    }
  };

  const skillDesc = descriptions[skill];
  if (score >= 70) return skillDesc[70];
  if (score >= 61) return skillDesc[61];
  if (score >= 46) return skillDesc[46];
  if (score >= 37) return skillDesc[37];
  if (score >= 20) return skillDesc[20];
  return skillDesc[10];
};

// 生成PDF文档的函数 - 将页面截图转换为PDF
const generatePDFDocument = async (data: ScoreData) => {
  try {
    // 创建PDF
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // 处理第一页
    const page1Element = document.querySelector('#report-content-page1');
    if (!page1Element) {
      throw new Error('找不到第一页内容');
    }

    // 临时隐藏按钮
    const buttons = page1Element.querySelectorAll('button');
    const originalDisplays: string[] = [];
    buttons.forEach((button, index) => {
      originalDisplays[index] = (button as HTMLElement).style.display;
      (button as HTMLElement).style.display = 'none';
    });

    // 截取第一页
    const canvas1 = await html2canvas(page1Element as HTMLElement, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: page1Element.scrollWidth,
      height: page1Element.scrollHeight,
      scrollX: 0,
      scrollY: 0,
    });

    // 恢复按钮显示
    buttons.forEach((button, index) => {
      (button as HTMLElement).style.display = originalDisplays[index];
    });

    // 处理第二页
    const page2Element = document.querySelector('#report-content-page2');
    if (!page2Element) {
      throw new Error('找不到第二页内容');
    }

    // 截取第二页
    const canvas2 = await html2canvas(page2Element as HTMLElement, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: page2Element.scrollWidth,
      height: page2Element.scrollHeight,
      scrollX: 0,
      scrollY: 0,
    });

    // 计算图片在PDF中的尺寸
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();

    // 添加第一页
    const imgData1 = canvas1.toDataURL('image/jpeg', 1.0);
    const imgWidth1 = canvas1.width;
    const imgHeight1 = canvas1.height;
    const ratio1 = Math.min(pdfWidth / imgWidth1, pdfHeight / imgHeight1);
    const scaledWidth1 = imgWidth1 * ratio1;
    const scaledHeight1 = imgHeight1 * ratio1;
    const x1 = (pdfWidth - scaledWidth1) / 2;
    const y1 = (pdfHeight - scaledHeight1) / 2;
    pdf.addImage(imgData1, 'JPEG', x1, y1, scaledWidth1, scaledHeight1);

    // 添加新页面
    pdf.addPage();

    // 添加第二页
    const imgData2 = canvas2.toDataURL('image/jpeg', 1.0);
    const imgWidth2 = canvas2.width;
    const imgHeight2 = canvas2.height;
    const ratio2 = Math.min(pdfWidth / imgWidth2, pdfHeight / imgHeight2);
    const scaledWidth2 = imgWidth2 * ratio2;
    const scaledHeight2 = imgHeight2 * ratio2;
    const x2 = (pdfWidth - scaledWidth2) / 2;
    const y2 = (pdfHeight - scaledHeight2) / 2;
    pdf.addImage(imgData2, 'JPEG', x2, y2, scaledWidth2, scaledHeight2);

    // 下载PDF
    pdf.save(`AIET成绩报告_${data.studentName}_${data.testDate.replace(/\s+/g, '_')}.pdf`);

  } catch (error) {
    console.error('生成PDF时出错:', error);
    throw error;
  }
};

// CSV解析函数
const parseCSV = (csvText: string): ScoreData[] => {
  const lines = csvText.split('\n');
  if (lines.length < 2) return []; // 需要至少包含标题行和一行数据

  // 移除可能的回车符并分割每行
  const rows = lines.map(line => line.trim().replace('\r', '').split(','));
  const headers = rows[0].map(h => h.trim());

  // 验证CSV格式是否正确
  const requiredHeaders = ['testDate', 'studentName', 'testNumber', 'speaking', 'listening', 'writing', 'reading'];
  const hasAllHeaders = requiredHeaders.every(header => headers.includes(header));
  if (!hasAllHeaders) {
    throw new Error('CSV格式不正确，请确保包含所有必需的列：testDate, studentName, testNumber, speaking, listening, writing, reading');
  }

  // 解析数据行
  const data: ScoreData[] = [];
  for (let i = 1; i < rows.length; i++) {
    if (rows[i].length !== headers.length || rows[i].every(cell => cell === '')) continue; // 跳过空行或格式不正确的行

    const rowData: any = {};
    headers.forEach((header, index) => {
      const value = rows[i][index].trim();
      if (['speaking', 'listening', 'writing', 'reading'].includes(header)) {
        rowData[header] = parseFloat(value) || 0;
      } else {
        rowData[header] = value;
      }
    });

    // 计算总分
    rowData.overallScore = Math.round(
      (rowData.speaking + rowData.listening + rowData.writing + rowData.reading) / 4
    );

    data.push(rowData as ScoreData);
  }

  return data;
};


export default function App() {
  const [showForm, setShowForm] = useState(false);
  const [batchData, setBatchData] = useState<ScoreData[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [scoreData, setScoreData] = useState<ScoreData>({
    testDate: '5 June 2022',
    studentName: 'ZHANG SANFENG',
    testNumber: '50966447',
    overallScore: 54,
    speaking: 48,
    listening: 53,
    writing: 60,
    reading: 55
  });
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (field: keyof ScoreData, value: string | number) => {
    setScoreData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAddToBatch = () => {
    setBatchData(prev => [...prev, { ...scoreData }]);
    setShowForm(false);
  };

  const handleRemoveFromBatch = (index: number) => {
    setBatchData(prev => prev.filter((_, i) => i !== index));
  };

  const handleViewData = (index: number) => {
    setScoreData(batchData[index]);
    setCurrentIndex(index);
  };

  const handleGeneratePDF = async () => {
    try {
      if (batchData.length === 0) {
        // 如果没有批量数据，则只生成当前数据的PDF
        await generatePDFDocument(scoreData);
        return;
      }

      // 批量生成PDF
      for (let i = 0; i < batchData.length; i++) {
        const data = batchData[i];
        setScoreData(data); // 更新当前显示的数据
        setCurrentIndex(i);
        
        // 等待DOM更新
        await new Promise(resolve => setTimeout(resolve, 500));
        
        await generatePDFDocument(data);
      }

      alert('所有PDF生成完成！');
    } catch (error) {
      console.error('生成PDF时出错:', error);
      alert('生成PDF失败，请重试');
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const text = e.target?.result as string;
        const newData = parseCSV(text);
        
        if (newData.length > 0) {
          setBatchData(prev => [...prev, ...newData]);
          alert(`成功导入 ${newData.length} 条数据`);
        } else {
          alert('没有找到有效的数据');
        }
      } catch (error) {
        alert(error instanceof Error ? error.message : '导入CSV文件失败');
      }
    };
    reader.onerror = () => {
      alert('读取文件失败');
    };
    reader.readAsText(file);

    // 清除文件输入，这样同一个文件可以重复选择
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const calculateOverallScore = () => {
    const avg = (scoreData.speaking + scoreData.listening + scoreData.writing + scoreData.reading) / 4;
    setScoreData(prev => ({
      ...prev,
      overallScore: Math.round(avg)
    }));
  };

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      {/* 批量数据列表 */}
      <div className="max-w-6xl mx-auto mb-4 bg-white p-4 rounded shadow">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">批量数据列表</h2>
          <div className="flex gap-2">
            <input
              type="file"
              accept=".csv"
              onChange={handleFileUpload}
              ref={fileInputRef}
              className="hidden"
              id="csv-upload"
            />
            <label
              htmlFor="csv-upload"
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded text-sm font-medium cursor-pointer"
            >
              导入CSV
            </label>
            <button
              onClick={() => setShowForm(true)}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm font-medium"
            >
              添加数据
            </button>
            <button
              onClick={handleGeneratePDF}
              className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded text-sm font-medium"
            >
              {batchData.length > 0 ? '批量生成PDF' : '生成PDF'}
            </button>
          </div>
        </div>

        {/* CSV格式说明 */}
        <div className="mb-4 p-4 bg-gray-50 rounded text-sm text-gray-600">
          <h3 className="font-medium mb-2">CSV文件格式说明：</h3>
          <p>第一行必须包含以下列标题（顺序不限）：</p>
          <code className="block mt-1 mb-2 font-mono bg-gray-100 p-2 rounded">
            testDate,studentName,testNumber,speaking,listening,writing,reading
          </code>
          <p>示例数据行：</p>
          <code className="block mt-1 font-mono bg-gray-100 p-2 rounded">
            5 June 2022,ZHANG SANFENG,50966447,48,53,60,55
          </code>
        </div>

        {batchData.length > 0 ? (
          <div className="grid grid-cols-1 gap-2">
            {batchData.map((data, index) => (
              <div
                key={index}
                className={`flex justify-between items-center p-3 rounded border ${
                  index === currentIndex ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                }`}
              >
                <div className="flex gap-8">
                  <span className="font-medium">{data.studentName}</span>
                  <span className="text-gray-600">{data.testDate}</span>
                  <span className="text-gray-600">总分: {data.overallScore}</span>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleViewData(index)}
                    className="text-blue-500 hover:text-blue-600"
                  >
                    查看
                  </button>
                  <button
                    onClick={() => handleRemoveFromBatch(index)}
                    className="text-red-500 hover:text-red-600"
                  >
                    删除
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center text-gray-500 py-8">
            暂无批量数据，请点击"导入CSV"或"添加数据"按钮添加数据
          </div>
        )}
      </div>

      {/* 输入表单 */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-8 rounded-lg max-w-md w-full mx-4">
            <h2 className="text-xl font-bold mb-6">输入成绩数据</h2>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">测试完成日期</label>
                <input
                  type="text"
                  value={scoreData.testDate}
                  onChange={(e) => handleInputChange('testDate', e.target.value)}
                  className="w-full border border-gray-300 rounded px-3 py-2"
                  placeholder="例: 5 June 2022"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">学生姓名</label>
                <input
                  type="text"
                  value={scoreData.studentName}
                  onChange={(e) => handleInputChange('studentName', e.target.value)}
                  className="w-full border border-gray-300 rounded px-3 py-2"
                  placeholder="例: ZHANG SANFENG"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">测试编号</label>
                <input
                  type="text"
                  value={scoreData.testNumber}
                  onChange={(e) => handleInputChange('testNumber', e.target.value)}
                  className="w-full border border-gray-300 rounded px-3 py-2"
                  placeholder="例: 50966447"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">口语 (Speaking)</label>
                  <input
                    type="number"
                    min="0"
                    max="80"
                    value={scoreData.speaking}
                    onChange={(e) => handleInputChange('speaking', parseInt(e.target.value) || 0)}
                    className="w-full border border-gray-300 rounded px-3 py-2"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">听力 (Listening)</label>
                  <input
                    type="number"
                    min="0"
                    max="80"
                    value={scoreData.listening}
                    onChange={(e) => handleInputChange('listening', parseInt(e.target.value) || 0)}
                    className="w-full border border-gray-300 rounded px-3 py-2"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">写作 (Writing)</label>
                  <input
                    type="number"
                    min="0"
                    max="80"
                    value={scoreData.writing}
                    onChange={(e) => handleInputChange('writing', parseInt(e.target.value) || 0)}
                    className="w-full border border-gray-300 rounded px-3 py-2"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">阅读 (Reading)</label>
                  <input
                    type="number"
                    min="0"
                    max="80"
                    value={scoreData.reading}
                    onChange={(e) => handleInputChange('reading', parseInt(e.target.value) || 0)}
                    className="w-full border border-gray-300 rounded px-3 py-2"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">总分 (Overall Score)</label>
                <div className="flex gap-2">
                  <input
                    type="number"
                    min="0"
                    max="80"
                    value={scoreData.overallScore}
                    onChange={(e) => handleInputChange('overallScore', parseInt(e.target.value) || 0)}
                    className="flex-1 border border-gray-300 rounded px-3 py-2"
                  />
                  <button
                    onClick={calculateOverallScore}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded text-sm"
                  >
                    自动计算
                  </button>
                </div>
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={() => setShowForm(false)}
                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-2 rounded"
              >
                取消
              </button>
              <button
                onClick={handleAddToBatch}
                className="flex-1 bg-orange-500 hover:bg-orange-600 text-white py-2 rounded"
              >
                添加到批量列表
              </button>
            </div>
          </div>
        </div>
      )}

      {/* First Page */}
      <div className="max-w-6xl mx-auto bg-white mb-8 shadow-md" id="report-content-page1">
        {/* First page content */}
        <div className="flex flex-col h-fit">  {/* Fit content height */}
          {/* Header with orange border */}
          <div className="border-b-4 border-orange-500 px-8 py-6">
            <div className="flex justify-between items-center">
              <h1 className="text-2xl font-bold text-black">
                Anderson Institute English Test (AIET)
              </h1>
              <div className="flex gap-12 text-sm">
                <div className="text-center">
                  <div className="text-gray-500 mb-1">Test Completion Date</div>
                  <div className="font-medium text-black">{scoreData.testDate}</div>
                </div>
                <div className="text-center">
                  <div className="text-gray-500 mb-1">Name</div>
                  <div className="font-medium text-black">{scoreData.studentName}</div>
                </div>
                <div className="text-center">
                  <div className="text-gray-500 mb-1">Test Number</div>
                  <div className="font-medium text-black">{scoreData.testNumber}</div>
                </div>
                <div className="text-center flex gap-2">
                  <button
                    onClick={() => setShowForm(true)}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm font-medium"
                  >
                    输入成绩
                  </button>
                  <button
                    onClick={handleGeneratePDF}
                    className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded text-sm font-medium"
                  >
                    导出PDF
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Warning Banner */}
          <div className="bg-orange-100 mx-8 mt-6 mb-6 p-4 rounded border-l-4 border-orange-500">
            <div className="flex items-start">
              <div className="w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                <span className="text-white text-xs font-bold">!</span>
              </div>
              <p className="text-sm text-orange-800">
                <strong>AIET scores are aligned to the Global Scale of English (GSE).</strong> The GSE runs from 10 to 90, with clear statements of what a learner can achieve at any point on the scale.
              </p>
            </div>
          </div>

          {/* Main Content */}
          <div className="px-8 pb-4 flex gap-8">
            {/* Left Column - 60% width */}
            <div className="w-3/5 space-y-6">

              {/* Module 1: Overall Versant Score */}
              <div className="bg-gray-100 p-6 rounded-lg">
                <div className="flex gap-8 items-center">
                  {/* Left side - Score info (centered) */}
                  <div className="flex-shrink-0 text-center">
                    <h3 className="text-lg font-medium text-black mb-4">Overall Versant Score</h3>
                    <div className="text-6xl font-bold text-black mb-4">{scoreData.overallScore || 0}</div>
                    <div className="flex items-center justify-center mb-4">
                      <span className="text-sm text-gray-600 mr-2">20</span>
                      <div className="w-32 h-3 bg-gray-300 relative rounded">
                        <div
                          className="h-full bg-orange-500 rounded"
                          style={{width: `${(((scoreData.overallScore || 0) - 20) / (80 - 20)) * 100}%`}}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-600 ml-2">80</span>
                    </div>
                    <div>
                      <span className="bg-gray-700 text-white px-3 py-1 rounded text-sm font-medium">
                        <span className="relative" style={{top: '-6px'}}>CEFR: {getCEFRLevel(scoreData.overallScore)}</span>
                      </span>
                    </div>
                  </div>

                  {/* Right side - Description */}
                  <div className="flex-1 text-sm text-gray-700 leading-relaxed">
                    <p>
                      {getScoreDescription(scoreData.overallScore)}
                    </p>
                  </div>
                </div>
              </div>

              {/* Module 2: Skills Scores */}
              <div className="bg-white p-6 rounded">
                <div className="flex gap-6 justify-center">
                  <div className="flex items-center gap-2">
                    <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                      <span className="relative" style={{top: '-6px'}}>{scoreData.speaking || 0}</span>
                    </div>
                    <span className="text-sm text-black font-medium">Speaking</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                      <span className="relative" style={{top: '-6px'}}>{scoreData.listening || 0}</span>
                    </div>
                    <span className="text-sm text-black font-medium">Listening</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                      <span className="relative" style={{top: '-6px'}}>{scoreData.writing || 0}</span>
                    </div>
                    <span className="text-sm text-black font-medium">Writing</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                      <span className="relative" style={{top: '-6px'}}>{scoreData.reading || 0}</span>
                    </div>
                    <span className="text-sm text-black font-medium">Reading</span>
                  </div>
                </div>
              </div>

              {/* Module 3: Chart */}
              <div className="bg-white p-6 rounded">
                <div className="flex gap-4">
                  {/* Y-axis labels */}
                  <div className="flex flex-col justify-between h-[36rem] text-xs text-gray-700">
                    {[80, 75, 70, 65, 60, 55, 50, 45, 40, 35, 30, 25, 20, 15, 10, 5, 0].map((value) => (
                      <div key={value} className="flex items-center gap-2 h-6">
                        <div className={`${value === 54 ? 'bg-orange-500' : 'bg-gray-600'} text-white px-1.5 py-0.5 rounded text-xs min-w-7 text-center`}>
                          <span className="relative" style={{top: '-6px'}}>
                            {value >= 70 ? 'C2' : 
                             value >= 60 ? 'C1' : 
                             value >= 46 ? 'B2' : 
                             value >= 37 ? 'B1' : 
                             value >= 20 ? 'A2' : 
                             value >= 10 ? 'A1' : '<A1'}
                          </span>
                        </div>
                        <span>{value}</span>
                      </div>
                    ))}
                  </div>

                  {/* Chart area */}
                  <div className="flex-1 relative">
                    {/* Chart container */}
                    <div id="chart-container" className="h-[36rem] flex items-end gap-1 border-l-2 border-b-2 border-gray-400 bg-gray-50 relative">
                      {/* Orange reference line with circle indicator */}
                      <div
                        className="absolute left-0 right-0 h-0.5 bg-orange-500 z-20 flex items-center"
                        style={{
                          bottom: `${((scoreData.overallScore || 0) / 80) * 97}%`,
                          transform: 'translateY(50%)'
                        }}
                      >
                        <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white text-sm font-bold -ml-4">
                          <span className="relative" style={{top: '-1px'}}>{scoreData.overallScore || 0}</span>
                        </div>
                      </div>

                      {/* Speaking bar */}
                      <div className="flex-1 flex flex-col items-center h-full justify-end">
                        <div
                          className="w-full bg-blue-600 relative flex items-center justify-center text-white font-bold text-lg chart-bar"
                          style={{
                            height: `${((scoreData.speaking || 0) / 80) * 97}%`,
                            minHeight: '40px'
                          }}
                        >
                          <span className="absolute" style={{top: '5px'}}>{scoreData.speaking || 0}</span>
                        </div>
                      </div>

                      {/* Listening bar */}
                      <div className="flex-1 flex flex-col items-center h-full justify-end">
                        <div
                          className="w-full bg-teal-600 relative flex items-center justify-center text-white font-bold text-lg chart-bar"
                          style={{
                            height: `${((scoreData.listening || 0) / 80) * 97}%`,
                            minHeight: '40px'
                          }}
                        >
                          <span className="absolute" style={{top: '5px'}}>{scoreData.listening || 0}</span>
                        </div>
                      </div>

                      {/* Writing bar */}
                      <div className="flex-1 flex flex-col items-center h-full justify-end">
                        <div
                          className="w-full bg-purple-600 relative flex items-center justify-center text-white font-bold text-lg chart-bar"
                          style={{
                            height: `${((scoreData.writing || 0) / 80) * 97}%`,
                            minHeight: '40px'
                          }}
                        >
                          <span className="absolute" style={{top: '5px'}}>{scoreData.writing || 0}</span>
                        </div>
                      </div>

                      {/* Reading bar */}
                      <div className="flex-1 flex flex-col items-center h-full justify-end">
                        <div
                          className="w-full bg-red-600 relative flex items-center justify-center text-white font-bold text-lg chart-bar"
                          style={{
                            height: `${((scoreData.reading || 0) / 80) * 97}%`,
                            minHeight: '40px'
                          }}
                        >
                          <span className="absolute" style={{top: '5px'}}>{scoreData.reading || 0}</span>
                        </div>
                      </div>
                    </div>

                    {/* X-axis labels */}
                    <div className="flex gap-1 mt-2 text-xs text-gray-700 -ml-16">
                      <div className="w-8 text-left">CEFR</div>
                      <div className="w-8 text-left">AIET</div>
                      <div className="flex-1 text-center">Speaking</div>
                      <div className="flex-1 text-center">Listening</div>
                      <div className="flex-1 text-center">Writing</div>
                      <div className="flex-1 text-center">Reading</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - 40% width */}
            <div className="w-2/5">
              {/* Understanding the Skills */}
              <div>
                <h3 className="text-xl font-bold text-black mb-6">Understanding the Skills</h3>

                <div className="mb-6">
                  <h4 className="font-bold text-black mb-3">Overall Score</h4>
                  <p className="text-sm text-gray-600 leading-relaxed">
                    The Overall score of the test represents the ability to understand spoken and written English and respond appropriately in speaking and writing on everyday topics, at an appropriate pace and in intelligible English. Scores are based on a weighted combination of the four skill scores.
                  </p>
                </div>

                <div>
                  <h4 className="font-bold text-black mb-3">AIET</h4>
                  <p className="text-sm text-gray-600 leading-relaxed">
                    Andersons Institute tests follow the GSE language testing system expressed second language performance on a 20-80 scale. The Global Scale of English is now used for many of these tests.
                  </p>
                </div>
              </div>

              {/* Additional Performance Indicators */}
              <div className="mt-40">
                <h3 className="text-xl font-bold text-black mb-6">Additional Performance Indicators</h3>

                <div className="mb-6">
                  <h4 className="font-bold text-black mb-3">Typing Speed</h4>
                  <p className="text-sm text-gray-600 leading-relaxed">
                    Typing speed is the number of words typed in one minute in the Typing task. For a valid Writing score, candidates should type faster than 12 WPM.
                  </p>
                </div>

                <div>
                  <h4 className="font-bold text-black mb-3">Typing Accuracy</h4>
                  <p className="text-sm text-gray-600 leading-relaxed">
                    Typing accuracy refers to the percentage correctly typed in the Typing task. For a valid Writing score, candidates should have at least 90% accuracy.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="px-8 pt-4 pb-2 mt-8">
            <div className="flex justify-between items-center border-t border-gray-200 pt-4">
              <div className="text-sm text-gray-700">
                <div className="font-medium">Global University Foundation Centre | Global University |</div>
                <div className="font-medium">Changhe Graduate School</div>
                <div className="mt-1">
                  For more information, visit us online at{' '}
                  <span className="text-blue-600 underline">www.anderson.org.cn</span>
                </div>
              </div>
              <div className="text-sm text-gray-700 font-medium">
                Page 1 of 2
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Second Page */}
      <div className="max-w-6xl mx-auto bg-white shadow-md" id="report-content-page2">
        <div className="p-12 flex flex-col h-fit">  {/* Fit content height */}
          {/* TN Number */}
          <div className="flex justify-end mb-8">
            <div className="text-right text-lg">
              <span className="text-gray-700">TN: </span>
              <span className="text-black font-medium">{scoreData.testNumber}</span>
            </div>
          </div>
          
          <div className="flex justify-between mb-12">
            <h2 className="text-2xl font-bold text-black">Current Capabilities in Detail</h2>
            <h2 className="text-2xl font-bold text-black">Understanding the Skills</h2>
          </div>

          <div className="flex gap-8">
            {/* Left Column - Current Capabilities */}
            <div className="w-[65%]">

              {/* Speaking Section */}
              <div className="mb-16">
                <div className="flex items-baseline mb-2">
                  <h3 className="text-2xl font-bold mr-2">Speaking:</h3>
                  <div className="bg-blue-600 text-white px-4 py-1 rounded text-base font-medium mr-auto">
                    AIET: {scoreData.speaking || 0}/80
                  </div>
                  <span className="text-orange-700 font-medium -ml-10 mr-2">CEFR: {getCEFRLevel(scoreData.speaking)}</span>
                </div>
                <p className="text-gray-700 text-lg leading-relaxed">
                  {getCEFRDescription(scoreData.speaking, 'speaking')}
                </p>
              </div>

              {/* Listening Section */}
              <div className="mb-16">
                <div className="flex items-baseline mb-2">
                  <h3 className="text-2xl font-bold mr-2">Listening:</h3>
                  <div className="bg-teal-600 text-white px-4 py-1 rounded text-base font-medium mr-auto">
                    AIET: {scoreData.listening || 0}/80
                  </div>
                  <span className="text-orange-700 font-medium -ml-10 mr-2">CEFR: {getCEFRLevel(scoreData.listening)}</span>
                </div>
                <p className="text-gray-700 text-lg leading-relaxed">
                  {getCEFRDescription(scoreData.listening, 'listening')}
                </p>
              </div>

              {/* Writing Section */}
              <div className="mb-16">
                <div className="flex items-baseline mb-2">
                  <h3 className="text-2xl font-bold mr-2">Writing:</h3>
                  <div className="bg-purple-600 text-white px-4 py-1 rounded text-base font-medium mr-auto">
                    AIET: {scoreData.writing || 0}/80
                  </div>
                  <span className="text-orange-700 font-medium -ml-10 mr-2">CEFR: {getCEFRLevel(scoreData.writing)}</span>
                </div>
                <p className="text-gray-700 text-lg leading-relaxed">
                  {getCEFRDescription(scoreData.writing, 'writing')}
                </p>
              </div>

              {/* Reading Section */}
              <div className="mb-16">
                <div className="flex items-baseline mb-2">
                  <h3 className="text-2xl font-bold mr-2">Reading:</h3>
                  <div className="bg-red-600 text-white px-4 py-1 rounded text-base font-medium mr-auto">
                    AIET: {scoreData.reading || 0}/80
                  </div>
                  <span className="text-orange-700 font-medium -ml-10 mr-2">CEFR: {getCEFRLevel(scoreData.reading)}</span>
                </div>
                <p className="text-gray-700 text-lg leading-relaxed">
                  {getCEFRDescription(scoreData.reading, 'reading')}
                </p>
              </div>
            </div>

            {/* Right Column - Understanding the Skills */}
            <div className="w-[35%] pl-8">
              {/* Speaking Understanding - 对应左侧Speaking */}
              <div className="mb-16 mt-8">
                <p className="text-sm text-gray-700 leading-relaxed">
                  {getSkillDescription(scoreData.speaking, 'speaking')}
                </p>
              </div>

              {/* Listening Understanding - 对应左侧Listening */}
              <div className="mb-16 mt-28">
                <p className="text-sm text-gray-700 leading-relaxed">
                  {getSkillDescription(scoreData.listening, 'listening')}
                </p>
              </div>

              {/* Writing Understanding - 对应左侧Writing */}
              <div className="mb-16 mt-24">
                <p className="text-sm text-gray-700 leading-relaxed">
                  {getSkillDescription(scoreData.writing, 'writing')}
                </p>
              </div>

              {/* Reading Understanding - 对应左侧Reading */}
              <div className="mb-16 mt-24">
                <p className="text-sm text-gray-700 leading-relaxed">
                  {getSkillDescription(scoreData.reading, 'reading')}
                </p>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="px-8 pt-4 pb-2 mt-96">
            <div className="flex justify-between items-center border-t border-gray-200 pt-4">
              <div className="text-sm text-gray-700">
                <div className="font-medium">Global University Foundation Centre | Global University |</div>
                <div className="font-medium">Changhe Graduate School</div>
                <div className="mt-1">
                  For more information, visit us online at{' '}
                  <span className="text-blue-600 underline">www.anderson.org.cn</span>
                </div>
              </div>
              <div className="text-sm text-gray-700 font-medium">
                Page 2 of 2
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}