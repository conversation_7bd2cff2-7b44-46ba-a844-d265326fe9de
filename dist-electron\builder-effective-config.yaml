directories:
  output: dist-electron
  buildResources: build
appId: com.aiet.report
productName: AIET Report
files:
  - filter:
      - dist/**/*
      - electron/**/*
      - node_modules/**/*
      - package.json
extraResources:
  - from: assets
    to: assets
    filter:
      - '**/*'
win:
  target:
    - target: nsis
      arch:
        - x64
  requestedExecutionLevel: asInvoker
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: AIET Report
mac:
  target: dmg
linux:
  target: AppImage
electronVersion: 37.2.3
