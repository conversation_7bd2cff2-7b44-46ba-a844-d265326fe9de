{"version": 3, "file": "BoundaryPointAlgorithm.js", "sourceRoot": "", "sources": ["../../src/algorithm/BoundaryPointAlgorithm.ts"], "names": [], "mappings": ";;AAAA,kDAAmE;AACnE,mDAGwB;AAExB;;;;;GAKG;AACH,SAAgB,sBAAsB,CAAC,EAAiB,EAAE,UAAyB;IAEjF,MAAM,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,CAAA;IACnB,MAAM,OAAO,GAAG,EAAE,CAAC,CAAC,CAAC,CAAA;IACrB,MAAM,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAA;IAC3B,MAAM,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,CAAA;IAE7B;;OAEG;IACH,OAAO,CAAC,MAAM,CAAC,6BAAa,CAAC,KAAK,CAAC,KAAK,6BAAa,CAAC,KAAK,CAAC,EAC1D,gDAAgD,CAAC,CAAA;IAEnD;;;;OAIG;IACH,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,IAAI,OAAO,KAAK,OAAO,EAAE;YACvB,OAAO,6BAAgB,CAAC,KAAK,CAAA;SAC9B;aAAM,IAAI,OAAO,GAAG,OAAO,EAAE;YAC5B,OAAO,6BAAgB,CAAC,MAAM,CAAA;SAC/B;aAAM;YACL,OAAO,6BAAgB,CAAC,KAAK,CAAA;SAC9B;KACF;IAED;;;;OAIG;IACH,IAAI,gCAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;QAClC,MAAM,GAAG,GAAG,sBAAsB,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAA;QACtE,IAAI,GAAG,KAAK,6BAAgB,CAAC,MAAM,EAAE;YACnC,OAAO,6BAAgB,CAAC,KAAK,CAAA;SAC9B;aAAM,IAAI,GAAG,KAAK,6BAAgB,CAAC,KAAK,EAAE;YACzC,OAAO,6BAAgB,CAAC,MAAM,CAAA;SAC/B;KACF;IAED;;OAEG;IACH,IAAI,iCAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;QACnC;;;;WAIG;QACH,IAAI,KAAK,GAAG,KAAK,CAAA;QAEjB,OAAO,CAAC,8BAAc,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;YACpC,0BAA0B;YAC1B,IAAI,KAAK,CAAC,OAAO,KAAK,IAAI,EAAE;gBAC1B,KAAK,GAAG,KAAK,CAAC,OAAO,CAAA;aACtB;SACF;QAED,IAAI,0BAAU,CAAC,KAAK,CAAC,GAAG,OAAO,EAAE;YAC/B,OAAO,6BAAgB,CAAC,KAAK,CAAA;SAC9B;KACF;IAED;;OAEG;IACH,OAAO,6BAAgB,CAAC,MAAM,CAAA;AAChC,CAAC;AArED,wDAqEC"}