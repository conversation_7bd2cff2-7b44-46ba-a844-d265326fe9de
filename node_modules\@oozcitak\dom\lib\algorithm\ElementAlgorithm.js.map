{"version": 3, "file": "ElementAlgorithm.js", "sourceRoot": "", "sources": ["../../src/algorithm/ElementAlgorithm.ts"], "names": [], "mappings": ";;AAAA,gCAA4B;AAE5B,2CAA6D;AAC7D,kCAA+B;AAC/B,sDAAyF;AACzF,uDAE0B;AAC1B,qEAKiC;AACjC,2EAAmF;AACnF,iDAA4D;AAC5D,2DAAwD;AACxD,2DAA+D;AAE/D;;;;;;GAMG;AACH,SAAgB,WAAW,CAAC,SAAe,EAAE,OAAgB;IAC3D;;OAEG;IACH,OAAO,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAA;AACpE,CAAC;AALD,kCAKC;AAED;;;;;;GAMG;AACH,SAAgB,cAAc,CAAC,SAAe,EAAE,OAAgB,EAAE,KAAa;IAC7E;;;OAGG;IACH,IAAI,SAAG,CAAC,QAAQ,CAAC,iBAAiB,EAAE;QAClC,iEAAqC,CAAC,OAAO,EAC3C,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC,MAAM,CAAC,CAAA;KAChE;IAED;;;;;OAKG;IACH,IAAI,SAAG,CAAC,QAAQ,CAAC,cAAc,EAAE;QAC/B,IAAI,YAAK,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE;YACtC,4EAAmD,CACjD,OAAO,EAAE,0BAA0B,EACnC,CAAC,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,CAAA;SACzE;KACF;IAED;;;;OAIG;IACH,IAAI,SAAG,CAAC,QAAQ,CAAC,KAAK,EAAE;QACtB,0CAA2B,CAAC,OAAO,EAAE,SAAS,CAAC,UAAU,EACvD,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,UAAU,CAAC,CAAA;KACjD;IAED,SAAS,CAAC,MAAM,GAAG,KAAK,CAAA;AAC1B,CAAC;AAnCD,wCAmCC;AAED;;;;;GAKG;AACH,SAAgB,cAAc,CAAC,SAAe,EAAE,OAAgB;IAC9D;;;OAGG;IACH,IAAI,SAAG,CAAC,QAAQ,CAAC,iBAAiB,EAAE;QAClC,iEAAqC,CAAC,OAAO,EAC3C,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;KACpD;IAED;;;;;OAKG;IACH,IAAI,SAAG,CAAC,QAAQ,CAAC,cAAc,EAAE;QAC/B,IAAI,YAAK,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE;YACtC,4EAAmD,CACjD,OAAO,EAAE,0BAA0B,EACnC,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,CAAA;SACxE;KACF;IAED;;;OAGG;IACH,IAAI,SAAG,CAAC,QAAQ,CAAC,KAAK,EAAE;QACtB,0CAA2B,CAAC,OAAO,EAAE,SAAS,CAAC,UAAU,EAAE,IAAI,EAC7D,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,UAAU,CAAC,CAAA;KAC1C;IAED;;;OAGG;IACH,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;IACjD,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAA;IAE5B,wCAAwC;IACxC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,cAAc,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,IAAI;QACzE,SAAS,CAAC,gBAAgB,KAAK,IAAI,IAAI,SAAS,CAAC,UAAU,KAAK,OAAO,CAAC,EAAE;QAC1E,OAAO,CAAC,aAAa,CAAC,cAAc,GAAG,IAAI,CAAA;KAC5C;AACH,CAAC;AA7CD,wCA6CC;AAED;;;;;GAKG;AACH,SAAgB,cAAc,CAAC,SAAe,EAAE,OAAgB;IAC9D;;;OAGG;IACH,IAAI,SAAG,CAAC,QAAQ,CAAC,iBAAiB,EAAE;QAClC,iEAAqC,CAAC,OAAO,EAC3C,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC,MAAM,CAAC,CAAA;KAChE;IAED;;;;;OAKG;IACH,IAAI,SAAG,CAAC,QAAQ,CAAC,cAAc,EAAE;QAC/B,IAAI,YAAK,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE;YACtC,4EAAmD,CACjD,OAAO,EAAE,0BAA0B,EACnC,CAAC,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,CAAA;SACxE;KACF;IAED;;;OAGG;IACH,IAAI,SAAG,CAAC,QAAQ,CAAC,KAAK,EAAE;QACtB,0CAA2B,CAAC,OAAO,EAAE,SAAS,CAAC,UAAU,EACvD,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,SAAS,CAAC,UAAU,CAAC,CAAA;KAChD;IAED;;;OAGG;IACH,MAAM,KAAK,GAAG,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;IAClE,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;IAClD,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAA;AAC3B,CAAC;AAxCD,wCAwCC;AAED;;;;;;GAMG;AACH,SAAgB,eAAe,CAAC,OAAa,EAAE,OAAa,EAC1D,OAAgB;IAChB;;;OAGG;IACH,IAAI,SAAG,CAAC,QAAQ,CAAC,iBAAiB,EAAE;QAClC,iEAAqC,CAAC,OAAO,EAC3C,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,CAAA;KAC1D;IAED;;;;;OAKG;IACH,IAAI,SAAG,CAAC,QAAQ,CAAC,cAAc,EAAE;QAC/B,IAAI,YAAK,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE;YACtC,4EAAmD,CACjD,OAAO,EAAE,0BAA0B,EACnC,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAA;SAC5E;KACF;IAED;;;OAGG;IACH,IAAI,SAAG,CAAC,QAAQ,CAAC,KAAK,EAAE;QACtB,0CAA2B,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,EACrD,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,UAAU,CAAC,CAAA;KACtD;IAED;;;;OAIG;IACH,MAAM,KAAK,GAAG,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;IAChE,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,OAAO,CAAA;KACnD;IACD,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAA;IACvB,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAA;IAE1B,wCAAwC;IACxC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,cAAc,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,IAAI;QACvE,OAAO,CAAC,gBAAgB,KAAK,IAAI,IAAI,OAAO,CAAC,UAAU,KAAK,OAAO,CAAC,EAAE;QACtE,OAAO,CAAC,aAAa,CAAC,cAAc,GAAG,IAAI,CAAA;KAC5C;AACH,CAAC;AAnDD,0CAmDC;AAED;;;;;GAKG;AACH,SAAgB,4BAA4B,CAAC,aAAqB,EAAE,OAAgB;IAElF;;;;;OAKG;IACH,IAAI,OAAO,CAAC,UAAU,KAAK,iBAAc,CAAC,IAAI,IAAI,OAAO,CAAC,aAAa,CAAC,KAAK,KAAK,MAAM,EAAE;QACxF,aAAa,GAAG,aAAa,CAAC,WAAW,EAAE,CAAA;KAC5C;IAED,OAAO,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,IAAI,CAC3C,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,KAAK,aAAa,CAAC,IAAI,IAAI,CAAA;AAC1D,CAAC;AAdD,oEAcC;AAED;;;;;;;GAOG;AACH,SAAgB,6CAA6C,CAAC,SAAiB,EAAE,SAAiB,EAChG,OAAgB;IAChB;;;;OAIG;IACH,MAAM,EAAE,GAAkB,SAAS,IAAI,IAAI,CAAA;IAC3C,OAAO,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,IAAI,CAC3C,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,KAAK,EAAE,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,IAAI,CAAA;AAC5E,CAAC;AAVD,sGAUC;AAED;;;;;;;GAOG;AACH,SAAgB,2BAA2B,CAAC,OAAgB,EAAE,SAAiB,EAC7E,YAAoB,EAAE;IACtB;;;;;OAKG;IACH,MAAM,IAAI,GAAG,6CAA6C,CAAC,SAAS,EAClE,SAAS,EAAE,OAAO,CAAC,CAAA;IACrB,IAAI,IAAI,KAAK,IAAI;QACf,OAAO,EAAE,CAAA;;QAET,OAAO,IAAI,CAAC,MAAM,CAAA;AACtB,CAAC;AAdD,kEAcC;AAED;;;;;GAKG;AACH,SAAgB,sBAAsB,CAAC,IAAU,EAAE,OAAgB;IACjE;;;;;;;;;OASG;IACH,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO;QACrD,MAAM,IAAI,kCAAmB,CAAC,kDAAkD,IAAI,CAAC,cAAc,kBAAkB,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,CAAC,CAAA;IAEvJ,MAAM,OAAO,GAAG,6CAA6C,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,EACjF,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;IAE3B,IAAI,OAAO,KAAK,IAAI;QAAE,OAAO,IAAI,CAAA;IACjC,IAAI,OAAO,KAAK,IAAI,EAAE;QACpB,eAAe,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAA;KACxC;SAAM;QACL,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;KAC9B;IAED,OAAO,OAAO,CAAA;AAChB,CAAC;AAzBD,wDAyBC;AAED;;;;;;;;GAQG;AACH,SAAgB,2BAA2B,CAAC,OAAgB,EAAE,SAAiB,EAC7E,KAAa,EAAE,SAAwB,IAAI,EAAE,YAA2B,IAAI;IAC5E;;;;;;;;;;OAUG;IACH,MAAM,SAAS,GAAG,6CAA6C,CAAC,SAAS,IAAI,EAAE,EAC7E,SAAS,EAAE,OAAO,CAAC,CAAA;IAErB,IAAI,SAAS,KAAK,IAAI,EAAE;QACtB,MAAM,OAAO,GAAG,6BAAW,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAA;QAC7D,OAAO,CAAC,UAAU,GAAG,SAAS,CAAA;QAC9B,OAAO,CAAC,gBAAgB,GAAG,MAAM,CAAA;QACjC,OAAO,CAAC,MAAM,GAAG,KAAK,CAAA;QACtB,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QAChC,OAAM;KACP;IAED,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE,KAAK,CAAC,CAAA;AAC3C,CAAC;AA1BD,kEA0BC;AAED;;;;;GAKG;AACH,SAAgB,+BAA+B,CAAC,aAAqB,EAAE,OAAgB;IAErF;;;;;OAKG;IACH,MAAM,IAAI,GAAG,4BAA4B,CAAC,aAAa,EAAE,OAAO,CAAC,CAAA;IACjE,IAAI,IAAI,KAAK,IAAI,EAAE;QACjB,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;KAC9B;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AAbD,0EAaC;AAED;;;;;;;GAOG;AACH,SAAgB,gDAAgD,CAAC,SAAiB,EAAE,SAAiB,EACnG,OAAgB;IAChB;;;;OAIG;IACH,MAAM,IAAI,GAAG,6CAA6C,CAAC,SAAS,EAClE,SAAS,EAAE,OAAO,CAAC,CAAA;IACrB,IAAI,IAAI,KAAK,IAAI,EAAE;QACjB,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;KAC9B;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AAbD,4GAaC;AAED;;;;;;;;;;GAUG;AACH,SAAgB,uBAAuB,CAAC,QAAkB,EAAE,SAAiB,EAC3E,SAAwB,EAAE,SAAwB,IAAI,EACtD,KAAoB,IAAI,EACxB,gCAAyC,KAAK;IAE9C;;;;OAIG;IACH,IAAI,MAAM,GAAmB,IAAI,CAAA;IAEjC,IAAI,CAAC,SAAG,CAAC,QAAQ,CAAC,cAAc,EAAE;QAChC,MAAM,GAAG,gCAAc,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAA;QAC/D,MAAM,CAAC,mBAAmB,GAAG,cAAc,CAAA;QAC3C,MAAM,CAAC,wBAAwB,GAAG,IAAI,CAAA;QACtC,MAAM,CAAC,GAAG,GAAG,EAAE,CAAA;QAEf,OAAO,MAAM,CAAA;KACd;IAED;;;OAGG;IACH,MAAM,UAAU,GAAG,qEAA4C,CAC7D,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,CACnC,CAAA;IAED,IAAI,UAAU,KAAK,IAAI,IAAI,UAAU,CAAC,IAAI,KAAK,UAAU,CAAC,SAAS,EAAE;QACnE;;;;;;;;;;;;;;;YAeI;QACJ,MAAM,eAAe,GAAG,6CAAyB,CAAC,SAAS,EAAE,iBAAc,CAAC,IAAI,CAAC,CAAA;QACjF,MAAM,GAAG,IAAI,eAAe,EAAE,CAAA;QAC9B,MAAM,CAAC,UAAU,GAAG,SAAS,CAAA;QAC7B,MAAM,CAAC,UAAU,GAAG,iBAAc,CAAC,IAAI,CAAA;QACvC,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAA;QAChC,MAAM,CAAC,mBAAmB,GAAG,WAAW,CAAA;QACxC,MAAM,CAAC,wBAAwB,GAAG,IAAI,CAAA;QACtC,MAAM,CAAC,GAAG,GAAG,EAAE,CAAA;QACf,MAAM,CAAC,aAAa,GAAG,QAAQ,CAAA;QAC/B,IAAI,6BAA6B,EAAE;YACjC,8CAAqB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;SAC1C;aAAM;YACL,2EAAkD,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;SACvE;KACF;SAAM,IAAI,UAAU,KAAK,IAAI,EAAE;QAC9B;;WAEG;QACH,IAAI,6BAA6B,EAAE;YACjC;;;eAGG;YACH,IAAI;gBACF;;;;;;;;mBAQG;gBACH,MAAM,CAAC,GAAG,UAAU,CAAC,WAAW,CAAA;gBAChC,MAAM,MAAM,GAAG,IAAI,CAAC,EAAE,CAAA;gBACtB,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,mBAAmB,KAAK,SAAS,CAAC,CAAA;gBACxD,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,wBAAwB,KAAK,SAAS,CAAC,CAAA;gBAC7D,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,KAAK,iBAAc,CAAC,IAAI,CAAC,CAAA;gBAEzD;;;;;;;;;;;mBAWG;gBACH,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC;oBAAE,MAAM,IAAI,gCAAiB,CAAC,wCAAwC,CAAC,CAAA;gBAC7G,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC;oBAAE,MAAM,IAAI,gCAAiB,CAAC,yCAAyC,CAAC,CAAA;gBACvG,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI;oBAAE,MAAM,IAAI,gCAAiB,CAAC,2CAA2C,CAAC,CAAA;gBACrG,IAAI,MAAM,CAAC,aAAa,KAAK,QAAQ;oBAAE,MAAM,IAAI,gCAAiB,CAAC,0CAA0C,CAAC,CAAA;gBAC9G,IAAI,MAAM,CAAC,UAAU,KAAK,SAAS;oBAAE,MAAM,IAAI,gCAAiB,CAAC,4CAA4C,CAAC,CAAA;gBAE9G;;;mBAGG;gBACH,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAA;gBAChC,MAAM,CAAC,GAAG,GAAG,IAAI,CAAA;aAClB;YAAC,OAAO,CAAC,EAAE;gBACV;;;;;;;;mBAQG;gBACH,6BAA6B;gBAC7B,MAAM,GAAG,2CAAyB,CAAC,QAAQ,EAAE,SAAS,EACpD,iBAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;gBAC9B,MAAM,CAAC,mBAAmB,GAAG,QAAQ,CAAA;gBACrC,MAAM,CAAC,wBAAwB,GAAG,IAAI,CAAA;gBACtC,MAAM,CAAC,GAAG,GAAG,IAAI,CAAA;aAClB;SACF;aAAM;YACL;;;;;;;;;eASG;YACH,MAAM,GAAG,oCAAkB,CAAC,QAAQ,EAAE,SAAS,EAC7C,iBAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;YAC9B,MAAM,CAAC,mBAAmB,GAAG,WAAW,CAAA;YACxC,MAAM,CAAC,wBAAwB,GAAG,IAAI,CAAA;YACtC,MAAM,CAAC,GAAG,GAAG,IAAI,CAAA;YACjB,2EAAkD,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;SACvE;KACF;SAAM;QACL;;;;;;;;;WASG;QACH,MAAM,gBAAgB,GAAG,6CAAyB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAA;QACxE,MAAM,GAAG,IAAI,gBAAgB,EAAE,CAAA;QAC/B,MAAM,CAAC,UAAU,GAAG,SAAS,CAAA;QAC7B,MAAM,CAAC,UAAU,GAAG,SAAS,CAAA;QAC7B,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAA;QAChC,MAAM,CAAC,mBAAmB,GAAG,cAAc,CAAA;QAC3C,MAAM,CAAC,wBAAwB,GAAG,IAAI,CAAA;QACtC,MAAM,CAAC,GAAG,GAAG,EAAE,CAAA;QACf,MAAM,CAAC,aAAa,GAAG,QAAQ,CAAA;QAE/B;;;;WAIG;QACH,IAAI,SAAS,KAAK,iBAAc,CAAC,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI;YACnD,+DAAsC,CAAC,SAAS,CAAC,CAAC,EAAE;YACpD,MAAM,CAAC,mBAAmB,GAAG,WAAW,CAAA;SACzC;KACF;IAED,0BAA0B;IAC1B,IAAI,MAAM,KAAK,IAAI,EAAE;QACnB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;KAC7C;IAED;;OAEG;IACH,OAAO,MAAM,CAAA;AACf,CAAC;AAzLD,0DAyLC;AAED;;;;;;;;;;GAUG;AACH,SAAgB,sBAAsB,CAAC,OAAgB,EACrD,KAA8D,EAC9D,IAAU;IACV;;;;;;;;;;;;;;;OAeG;IACH,QAAQ,KAAK,CAAC,WAAW,EAAE,EAAE;QAC3B,KAAK,aAAa;YAChB,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI;gBAAE,OAAO,IAAI,CAAA;YACzC,OAAO,sCAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,EAC7C,OAAO,CAAC,CAAA;QACZ,KAAK,YAAY;YACf,OAAO,sCAAkB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,WAAW,CAAC,CAAA;QAC/D,KAAK,WAAW;YACd,OAAO,sCAAkB,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAA;QAChD,KAAK,UAAU;YACb,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI;gBAAE,OAAO,IAAI,CAAA;YACzC,OAAO,sCAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,EAC7C,OAAO,CAAC,YAAY,CAAC,CAAA;QACzB;YACE,MAAM,IAAI,0BAAW,CAAC,2FAA2F,CAAC,CAAA;KACrH;AACH,CAAC;AAnCD,wDAmCC"}