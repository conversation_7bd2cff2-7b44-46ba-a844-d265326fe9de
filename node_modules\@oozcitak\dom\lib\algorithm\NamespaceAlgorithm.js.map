{"version": 3, "file": "NamespaceAlgorithm.js", "sourceRoot": "", "sources": ["../../src/algorithm/NamespaceAlgorithm.ts"], "names": [], "mappings": ";;AAAA,sDAA2E;AAC3E,2CAA6D;AAC7D,iDAAwD;AAExD;;;;GAIG;AACH,SAAgB,kBAAkB,CAAC,aAAqB;IACtD;;;;OAIG;IACH,IAAI,CAAC,yBAAU,CAAC,aAAa,CAAC;QAC5B,MAAM,IAAI,oCAAqB,CAAC,qBAAqB,aAAa,EAAE,CAAC,CAAA;IAEvE,IAAI,CAAC,0BAAW,CAAC,aAAa,CAAC;QAC7B,MAAM,IAAI,oCAAqB,CAAC,+BAA+B,aAAa,GAAG,CAAC,CAAA;AACpF,CAAC;AAXD,gDAWC;AAED;;;;;;;;;GASG;AACH,SAAgB,4BAA4B,CAAC,SAAwB,EAAE,aAAqB;IAG1F;;;;;;;;;;;;;;;;OAgBG;IACH,IAAI,CAAC,SAAS;QAAE,SAAS,GAAG,IAAI,CAAA;IAEhC,kBAAkB,CAAC,aAAa,CAAC,CAAA;IAEjC,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IACtC,MAAM,MAAM,GAAG,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;IACrD,MAAM,SAAS,GAAG,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAA;IAEjE,IAAI,MAAM,IAAI,SAAS,KAAK,IAAI;QAC9B,MAAM,IAAI,6BAAc,CAAC,6DAA6D,CAAC,CAAA;IAEzF,IAAI,MAAM,KAAK,KAAK,IAAI,SAAS,KAAK,iBAAc,CAAC,GAAG;QACtD,MAAM,IAAI,6BAAc,CAAC,sFAAsF,CAAC,CAAA;IAElH,IAAI,SAAS,KAAK,iBAAc,CAAC,KAAK;QACpC,CAAC,MAAM,KAAK,OAAO,IAAI,aAAa,KAAK,OAAO,CAAC;QACjD,MAAM,IAAI,6BAAc,CAAC,0FAA0F,CAAC,CAAA;IAEtH,IAAI,SAAS,KAAK,iBAAc,CAAC,KAAK;QACpC,CAAC,MAAM,KAAK,OAAO,IAAI,aAAa,KAAK,OAAO,CAAC;QACjD,MAAM,IAAI,6BAAc,CAAC,8FAA8F,CAAC,CAAA;IAE1H,OAAO,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,CAAC,CAAA;AACvC,CAAC;AA3CD,oEA2CC;AAED;;;;;;GAMG;AACH,SAAgB,sBAAsB,CAAC,aAAqB;IAE1D,kBAAkB,CAAC,aAAa,CAAC,CAAA;IAEjC,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IACtC,MAAM,MAAM,GAAG,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;IACrD,MAAM,SAAS,GAAG,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAA;IAEjE,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;AAC5B,CAAC;AATD,wDASC"}