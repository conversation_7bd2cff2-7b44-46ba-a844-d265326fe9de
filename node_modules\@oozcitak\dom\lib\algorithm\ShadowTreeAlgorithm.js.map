{"version": 3, "file": "ShadowTreeAlgorithm.js", "sourceRoot": "", "sources": ["../../src/algorithm/ShadowTreeAlgorithm.ts"], "names": [], "mappings": ";;AAAA,gCAA4B;AAE5B,kCAAqC;AACrC,yCAAwC;AACxC,mDAEwB;AACxB,2EAAsF;AAEtF;;;;GAIG;AACH,SAAgB,4BAA4B,CAAC,IAAU;IACrD;;;OAGG;IACH,MAAM,MAAM,GAAG,SAAG,CAAC,MAAM,CAAA;IACzB,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IAC7B,oEAAwC,EAAE,CAAA;AAC5C,CAAC;AARD,oEAQC;AAED;;;;;GAKG;AACH,SAAgB,sBAAsB,CAAC,OAAgB;IACrD;;OAEG;IACH,OAAO,YAAK,CAAC,cAAc,CAAC,6BAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAA;AAC3D,CAAC;AALD,wDAKC;AAED;;;;GAIG;AACH,SAAgB,qBAAqB,CAAC,QAAkB;IACtD;;OAEG;IACH,OAAO,CAAC,QAAQ,CAAC,aAAa,KAAK,IAAI,CAAC,CAAA;AAC1C,CAAC;AALD,sDAKC;AAED;;;;;GAKG;AACH,SAAgB,oBAAoB,CAAC,QAAkB,EAAE,WAC7C,KAAK;IACf;;;;;;;;OAQG;IACH,MAAM,IAAI,GAAG,WAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;IAClC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAyB,CAAA;IAC7C,IAAI,MAAM,KAAK,IAAI;QAAE,OAAO,IAAI,CAAA;IAChC,MAAM,MAAM,GAAI,MAAM,CAAC,WAAiC,IAAI,IAAI,CAAA;IAChE,IAAI,MAAM,KAAK,IAAI;QAAE,OAAO,IAAI,CAAA;IAChC,IAAI,QAAQ,IAAI,MAAM,CAAC,KAAK,KAAK,MAAM;QAAE,OAAO,IAAI,CAAA;IAEpD,IAAI,KAAK,GAAG,2CAA2B,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,YAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;IACpF,OAAO,KAAK,KAAK,IAAI,EAAE;QACrB,IAAK,KAAc,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK;YAAE,OAAQ,KAAc,CAAA;QACpE,KAAK,GAAG,0CAA0B,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,YAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;KACvF;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAzBD,oDAyBC;AAED;;;;GAIG;AACH,SAAgB,wBAAwB,CAAC,IAAU;IACjD;;;OAGG;IACH,MAAM,MAAM,GAAe,EAAE,CAAA;IAC7B,MAAM,IAAI,GAAG,6BAAa,CAAC,IAAI,CAAC,CAAA;IAChC,IAAI,CAAC,YAAK,CAAC,YAAY,CAAC,IAAI,CAAC;QAAE,OAAO,MAAM,CAAA;IAE5C;;;OAGG;IACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAA;IACvB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;QACrC,IAAI,YAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;YAC9B;;;eAGG;YACH,MAAM,SAAS,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAA;YAChD,IAAI,SAAS,KAAK,IAAI,EAAE;gBACtB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;aACtB;SACF;KACF;IAED;;OAEG;IACH,OAAO,MAAM,CAAA;AACf,CAAC;AA/BD,4DA+BC;AAED;;;;GAIG;AACH,SAAgB,iCAAiC,CAAC,IAAU;IAC1D;;;OAGG;IACH,MAAM,MAAM,GAAe,EAAE,CAAA;IAC7B,MAAM,IAAI,GAAG,6BAAa,CAAC,IAAI,CAAC,CAAA;IAChC,IAAI,CAAC,YAAK,CAAC,YAAY,CAAC,IAAI,CAAC;QAAE,OAAO,MAAM,CAAA;IAE5C;;;;OAIG;IACH,MAAM,SAAS,GAAG,wBAAwB,CAAC,IAAI,CAAC,CAAA;IAChD,IAAI,cAAO,CAAC,SAAS,CAAC,EAAE;QACtB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YACrC,IAAI,YAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;gBAC9B,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;aACzB;SACF;KACF;IAED;;OAEG;IACH,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE;QAC5B;;WAEG;QACH,IAAI,YAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,YAAK,CAAC,YAAY,CAAC,6BAAa,CAAC,IAAI,CAAC,CAAC,EAAE;YACjE;;;eAGG;YACH,MAAM,eAAe,GAAG,iCAAiC,CAAC,IAAI,CAAC,CAAA;YAC/D,MAAM,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,CAAA;SAChC;aAAM;YACL;;eAEG;YACH,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SAClB;KACF;IAED;;OAEG;IACH,OAAO,MAAM,CAAA;AACf,CAAC;AAjDD,8EAiDC;AAED;;;;GAIG;AACH,SAAgB,0BAA0B,CAAC,IAAU;IACnD;;;;OAIG;IACH,MAAM,SAAS,GAAG,wBAAwB,CAAC,IAAI,CAAC,CAAA;IAChD,IAAI,SAAS,CAAC,MAAM,KAAK,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;QACnD,IAAI,cAAc,GAAG,IAAI,CAAA;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACzC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;gBAC3C,cAAc,GAAG,KAAK,CAAA;gBACtB,MAAK;aACN;SACF;QACD,IAAI,CAAC,cAAc,EAAE;YACnB,4BAA4B,CAAC,IAAI,CAAC,CAAA;SACnC;KACF;IAED;;;OAGG;IACH,IAAI,CAAC,cAAc,GAAG,SAAS,CAAA;IAC/B,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;QAChC,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAA;KAC9B;AACH,CAAC;AA5BD,gEA4BC;AAED;;;;GAIG;AACH,SAAgB,kCAAkC,CAAC,IAAU;IAC3D;;;OAGG;IACH,IAAI,UAAU,GAAG,2CAA2B,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAO,EAAE,EAAE,CAAC,YAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;IAC7F,OAAO,UAAU,KAAK,IAAI,EAAE;QAC1B,0BAA0B,CAAC,UAAkB,CAAC,CAAA;QAC9C,UAAU,GAAG,0CAA0B,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAO,EAAE,EAAE,CAAC,YAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;KACrG;AACH,CAAC;AAVD,gFAUC;AAED;;;;GAIG;AACH,SAAgB,sBAAsB,CAAC,QAAkB;IACvD;;;OAGG;IACH,MAAM,IAAI,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAA;IAC3C,IAAI,IAAI,KAAK,IAAI,EAAE;QACjB,0BAA0B,CAAC,IAAI,CAAC,CAAA;KACjC;AACH,CAAC;AATD,wDASC"}