{"version": 3, "file": "TextAlgorithm.js", "sourceRoot": "", "sources": ["../../src/algorithm/TextAlgorithm.ts"], "names": [], "mappings": ";;AAAA,gCAA4B;AAE5B,kCAA+B;AAC/B,sDAAoD;AACpD,uDAA+C;AAC/C,mDAEwB;AACxB,qEAEiC;AACjC,2DAAqD;AAErD;;;;;GAKG;AACH,SAAgB,wBAAwB,CAAC,IAAU,EAAE,OAAgB,KAAK;IACxE;;;;;OAKG;IACH,OAAO;QACL,CAAC,MAAM,CAAC,QAAQ,CAAC;YAEf,IAAI,WAAW,GAAgB,IAAI,CAAA;YACnC,OAAO,WAAW,IAAI,YAAK,CAAC,UAAU,CAAC,WAAW,CAAC,gBAAgB,CAAC,EAAE;gBACpE,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAA;aAC3C;YAED,OAAO;gBACL,IAAI;oBACF,IAAI,WAAW,IAAI,CAAC,CAAC,IAAI,IAAI,WAAW,KAAK,IAAI,CAAC,EAAE;wBAClD,IAAI,YAAK,CAAC,UAAU,CAAC,WAAW,CAAC,YAAY,CAAC,EAAE;4BAC9C,WAAW,GAAG,WAAW,CAAC,YAAY,CAAA;yBACvC;6BAAM;4BACL,WAAW,GAAG,IAAI,CAAA;yBACnB;qBACF;oBAED,IAAI,WAAW,KAAK,IAAI,EAAE;wBACxB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;qBACnC;yBAAM;wBACL,MAAM,MAAM,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,CAAA;wBAClD,IAAI,YAAK,CAAC,UAAU,CAAC,WAAW,CAAC,YAAY,CAAC,EAAE;4BAC9C,WAAW,GAAG,WAAW,CAAC,YAAY,CAAA;yBACvC;6BAAM;4BACL,WAAW,GAAG,IAAI,CAAA;yBACnB;wBAED,OAAO,MAAM,CAAA;qBACd;gBACH,CAAC;aACF,CAAA;QACH,CAAC;KACF,CAAA;AACH,CAAC;AAzCD,4DAyCC;AAED;;;;;GAKG;AACH,SAAgB,iCAAiC,CAAC,IAAU,EAAE,OAAgB,KAAK;IACjF;;;;;OAKG;IACH,OAAO;QACL,CAAC,MAAM,CAAC,QAAQ,CAAC;YAEf,IAAI,WAAW,GAAgB,IAAI,CAAA;YACnC,OAAO,WAAW,IAAI,YAAK,CAAC,mBAAmB,CAAC,WAAW,CAAC,gBAAgB,CAAC,EAAE;gBAC7E,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAA;aAC3C;YAED,OAAO;gBACL,IAAI;oBACF,IAAI,WAAW,IAAI,CAAC,CAAC,IAAI,IAAI,WAAW,KAAK,IAAI,CAAC,EAAE;wBAClD,IAAI,YAAK,CAAC,mBAAmB,CAAC,WAAW,CAAC,YAAY,CAAC,EAAE;4BACvD,WAAW,GAAG,WAAW,CAAC,YAAY,CAAA;yBACvC;6BAAM;4BACL,WAAW,GAAG,IAAI,CAAA;yBACnB;qBACF;oBAED,IAAI,WAAW,KAAK,IAAI,EAAE;wBACxB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;qBACnC;yBAAM;wBACL,MAAM,MAAM,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,CAAA;wBAClD,IAAI,YAAK,CAAC,mBAAmB,CAAC,WAAW,CAAC,YAAY,CAAC,EAAE;4BACvD,WAAW,GAAG,WAAW,CAAC,YAAY,CAAA;yBACvC;6BAAM;4BACL,WAAW,GAAG,IAAI,CAAA;yBACnB;wBAED,OAAO,MAAM,CAAA;qBACd;gBACH,CAAC;aACF,CAAA;QACH,CAAC;KACF,CAAA;AACH,CAAC;AAzCD,8EAyCC;AAED;;;;;GAKG;AACH,SAAgB,0BAA0B,CAAC,IAAU;IACnD;;;OAGG;IACH,IAAI,QAAQ,GAAG,EAAE,CAAA;IACjB,IAAI,IAAI,GAAG,2CAA2B,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,YAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;IACtF,OAAO,IAAI,KAAK,IAAI,EAAE;QACpB,QAAQ,IAAK,IAAa,CAAC,KAAK,CAAA;QAChC,IAAI,GAAG,0CAA0B,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,YAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;KACxF;IACD,OAAO,QAAQ,CAAA;AACjB,CAAC;AAZD,gEAYC;AAED;;;;;;GAMG;AACH,SAAgB,UAAU,CAAC,IAAU,EAAE,MAAc;IACnD;;;;OAIG;IACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAA;IAChC,IAAI,MAAM,GAAG,MAAM,EAAE;QACnB,MAAM,IAAI,6BAAc,EAAE,CAAA;KAC3B;IAED;;;;;;;;OAQG;IACH,MAAM,KAAK,GAAG,MAAM,GAAG,MAAM,CAAA;IAC7B,MAAM,OAAO,GAAG,oDAA2B,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,CAAA;IAChE,MAAM,OAAO,GAAG,6BAAW,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAA;IACxD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;IAC3B,IAAI,MAAM,KAAK,IAAI,EAAE;QACnB;;WAEG;QACH,mCAAe,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,CAAA;QAEnD;;;;;;;;;;;WAWG;QACH,KAAK,MAAM,KAAK,IAAI,SAAG,CAAC,SAAS,EAAE;YACjC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,EAAE;gBACxD,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,OAAO,CAAA;gBACzB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAA;aAC1B;YACD,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,EAAE;gBACpD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,CAAA;gBACvB,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,MAAM,CAAA;aACxB;YACD,MAAM,KAAK,GAAG,0BAAU,CAAC,IAAI,CAAC,CAAA;YAC9B,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG,CAAC,EAAE;gBAC/D,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAA;aAClB;YACD,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG,CAAC,EAAE;gBAC3D,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAA;aAChB;SACF;KACF;IAED;;;;OAIG;IACH,kDAAyB,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,CAAA;IAClD,OAAO,OAAO,CAAA;AAChB,CAAC;AApED,gCAoEC"}