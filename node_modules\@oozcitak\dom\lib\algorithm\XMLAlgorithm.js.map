{"version": 3, "file": "XMLAlgorithm.js", "sourceRoot": "", "sources": ["../../src/algorithm/XMLAlgorithm.ts"], "names": [], "mappings": ";;AAAA;;;;GAIG;AACH,SAAgB,UAAU,CAAC,IAAY;IACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;QAE1B,gBAAgB;QAChB,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ;YACnC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,QAAQ;YAChC,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,aAAa;YACrC,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;YACxB,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;YACxB,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC;YACzB,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,CAAC;YAC1B,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,MAAM,CAAC;YAC3B,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC;YAC5B,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC;YAC5B,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC;YAC5B,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC;YAC5B,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC;YAC5B,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC,EAAE;YAC9B,SAAQ;SACT;aAAM,IAAI,CAAC,KAAK,CAAC;YAChB,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,aAAa;gBACpC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,QAAQ;gBAChC,CAAC,CAAC,KAAK,IAAI,CAAC;gBACZ,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC;gBAC5B,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE;YACjC,SAAQ;SACT;QAED,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YACrD,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;YACjC,IAAI,EAAE,IAAI,MAAM,IAAI,EAAE,IAAI,MAAM,EAAE;gBAChC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,MAAM,GAAG,OAAO,CAAA;gBAChD,CAAC,EAAE,CAAA;gBAEH,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,IAAI,OAAO,EAAE;oBAChC,SAAQ;iBACT;aACF;SACF;QAED,OAAO,KAAK,CAAA;KACb;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AA7CD,gCA6CC;AAED;;;;GAIG;AACH,SAAgB,WAAW,CAAC,IAAY;IACtC,IAAI,UAAU,GAAG,KAAK,CAAA;IACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;QAE1B,gBAAgB;QAChB,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ;YACnC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,QAAQ;YAChC,CAAC,KAAK,EAAE,IAAI,MAAM;YAClB,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;YACxB,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;YACxB,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC;YACzB,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,CAAC;YAC1B,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,MAAM,CAAC;YAC3B,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC;YAC5B,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC;YAC5B,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC;YAC5B,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC;YAC5B,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC;YAC5B,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC,EAAE;YAC9B,SAAQ;SACT;aAAM,IAAI,CAAC,KAAK,CAAC;YAChB,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,aAAa;gBACpC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,QAAQ;gBAChC,CAAC,CAAC,KAAK,IAAI,CAAC;gBACZ,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC;gBAC5B,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE;YACjC,SAAQ;SACT;aAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI;YACpC,IAAI,UAAU;gBAAE,OAAO,KAAK,CAAA,CAAC,2BAA2B;YACxD,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC;gBAAE,OAAO,KAAK,CAAA,CAAC,4BAA4B;YACpE,UAAU,GAAG,IAAI,CAAA;YACjB,SAAQ;SACT;QAED,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YACrD,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;YACjC,IAAI,EAAE,IAAI,MAAM,IAAI,EAAE,IAAI,MAAM,EAAE;gBAChC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,MAAM,GAAG,OAAO,CAAA;gBAChD,CAAC,EAAE,CAAA;gBAEH,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,IAAI,OAAO,EAAE;oBAChC,SAAQ;iBACT;aACF;SACF;QAED,OAAO,KAAK,CAAA;KACb;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAnDD,kCAmDC;AAED;;;;GAIG;AACH,SAAgB,eAAe,CAAC,KAAa;IAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;QAE3B,yEAAyE;QACzE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG;YACrC,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,MAAM,CAAC;YAC1B,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC,EAAE;YAC9B,SAAQ;SACT;QAED,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACtD,MAAM,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;YAClC,IAAI,EAAE,IAAI,MAAM,IAAI,EAAE,IAAI,MAAM,EAAE;gBAChC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,MAAM,GAAG,OAAO,CAAA;gBAChD,CAAC,EAAE,CAAA;gBAEH,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,IAAI,QAAQ,EAAE;oBACjC,SAAQ;iBACT;aACF;SACF;QAED,OAAO,KAAK,CAAA;KACb;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AA3BD,0CA2BC;AAED;;;;;GAKG;AACH,SAAgB,eAAe,CAAC,KAAa;IAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,sEAAsE;QACtE,MAAM,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;QAE7B,yDAAyD;QACzD,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ;YACnC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,QAAQ;YAChC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,6BAA6B;YACrD,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,mBAAmB;YAC3D,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,QAAQ;YAChC,CAAC,KAAK,EAAE,IAAI,IAAI;YAChB,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,SAAS;YACzD,SAAQ;SACT;aAAM;YACL,OAAO,KAAK,CAAA;SACb;KACF;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AApBD,0CAoBC"}