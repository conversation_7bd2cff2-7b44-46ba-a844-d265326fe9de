"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const EventTargetImpl_1 = require("./EventTargetImpl");
const algorithm_1 = require("../algorithm");
/**
 * Represents a signal object that communicates with a DOM request and abort
 * it through an AbortController.
 */
class AbortSignalImpl extends EventTargetImpl_1.EventTargetImpl {
    /**
     * Initializes a new instance of `AbortSignal`.
     */
    constructor() {
        super();
        this._abortedFlag = false;
        this._abortAlgorithms = new Set();
    }
    /** @inheritdoc */
    get aborted() { return this._abortedFlag; }
    /** @inheritdoc */
    get onabort() {
        return algorithm_1.event_getterEventHandlerIDLAttribute(this, "onabort");
    }
    set onabort(val) {
        algorithm_1.event_setterEventHandlerIDLAttribute(this, "onabort", val);
    }
    /**
     * Creates a new `AbortSignal`.
     */
    static _create() {
        return new AbortSignalImpl();
    }
}
exports.AbortSignalImpl = AbortSignalImpl;
//# sourceMappingURL=AbortSignalImpl.js.map