{"version": 3, "file": "DOMException.js", "sourceRoot": "", "sources": ["../../src/dom/DOMException.ts"], "names": [], "mappings": ";;AAAA;;GAEG;AACH,MAAa,YAAa,SAAQ,KAAK;IAMrC;;;;OAIG;IACH,YAAY,IAAY,EAAE,UAAkB,EAAE;QAC5C,KAAK,CAAC,OAAO,CAAC,CAAA;QAEd,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IAClB,CAAC;CACF;AAhBD,oCAgBC;AAED,MAAa,kBAAmB,SAAQ,YAAY;IAClD;;MAEE;IACF,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAA;IACtC,CAAC;CACF;AAPD,gDAOC;AAED,MAAa,kBAAmB,SAAQ,YAAY;IAClD;;MAEE;IACF,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,oBAAoB,EAAE,uCAAuC,GAAG,OAAO,CAAC,CAAA;IAChF,CAAC;CACF;AAPD,gDAOC;AAED,MAAa,kBAAmB,SAAQ,YAAY;IAClD;;MAEE;IACF,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAA;IACtC,CAAC;CACF;AAPD,gDAOC;AAED,MAAa,0BAA2B,SAAQ,YAAY;IAC1D;;MAEE;IACF,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,4BAA4B,EAAE,kCAAkC,GAAG,OAAO,CAAC,CAAA;IACnF,CAAC;CACF;AAPD,gEAOC;AAED,MAAa,iBAAkB,SAAQ,YAAY;IACjD;;MAEE;IACF,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,mBAAmB,EAAE,kCAAkC,GAAG,OAAO,CAAC,CAAA;IAC1E,CAAC;CACF;AAPD,8CAOC;AAED,MAAa,mBAAoB,SAAQ,YAAY;IACnD;;MAEE;IACF,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,qBAAqB,EAAE,OAAO,CAAC,CAAA;IACvC,CAAC;CACF;AAPD,kDAOC;AAED,MAAa,iBAAkB,SAAQ,YAAY;IACjD;;MAEE;IACF,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,mBAAmB,EAAE,qCAAqC,GAAG,OAAO,CAAC,CAAA;IAC7E,CAAC;CACF;AAPD,8CAOC;AAED,MAAa,wBAAyB,SAAQ,YAAY;IACxD;;MAEE;IACF,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,0BAA0B,EAAE,8CAA8C,GAAG,OAAO,CAAC,CAAA;IAC7F,CAAC;CACF;AAPD,4DAOC;AAED,MAAa,cAAe,SAAQ,YAAY;IAC9C;;MAEE;IACF,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,gBAAgB,EAAE,6DAA6D,GAAG,OAAO,CAAC,CAAA;IAClG,CAAC;CACF;AAPD,wCAOC;AAED,MAAa,kBAAmB,SAAQ,YAAY;IAClD;;MAEE;IACF,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,oBAAoB,EAAE,yDAAyD,GAAG,OAAO,CAAC,CAAA;IAClG,CAAC;CACF;AAPD,gDAOC;AAED,MAAa,eAAgB,SAAQ,YAAY;IAC/C;;MAEE;IACF,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAA;IACnC,CAAC;CACF;AAPD,0CAOC;AAED,MAAa,iBAAkB,SAAQ,YAAY;IACjD;;MAEE;IACF,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAA;IACrC,CAAC;CACF;AAPD,8CAOC;AAED,MAAa,aAAc,SAAQ,YAAY;IAC7C;;MAEE;IACF,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,eAAe,EAAE,6BAA6B,GAAG,OAAO,CAAC,CAAA;IACjE,CAAC;CACF;AAPD,sCAOC;AAED,MAAa,YAAa,SAAQ,YAAY;IAC5C;;MAEE;IACF,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,cAAc,EAAE,4BAA4B,GAAG,OAAO,CAAC,CAAA;IAC/D,CAAC;CACF;AAPD,oCAOC;AAED,MAAa,UAAW,SAAQ,YAAY;IAC1C;;MAEE;IACF,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,YAAY,EAAE,6BAA6B,GAAG,OAAO,CAAC,CAAA;IAC9D,CAAC;CACF;AAPD,gCAOC;AAED,MAAa,gBAAiB,SAAQ,YAAY;IAChD;;MAEE;IACF,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,kBAAkB,EAAE,4CAA4C,GAAG,OAAO,CAAC,CAAA;IACnF,CAAC;CACF;AAPD,4CAOC;AAED,MAAa,kBAAmB,SAAQ,YAAY;IAClD;;MAEE;IACF,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,oBAAoB,EAAE,+BAA+B,GAAG,OAAO,CAAC,CAAA;IACxE,CAAC;CACF;AAPD,gDAOC;AAED,MAAa,YAAa,SAAQ,YAAY;IAC5C;;MAEE;IACF,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,cAAc,EAAE,2BAA2B,GAAG,OAAO,CAAC,CAAA;IAC9D,CAAC;CACF;AAPD,oCAOC;AAED,MAAa,oBAAqB,SAAQ,YAAY;IACpD;;MAEE;IACF,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,sBAAsB,EAAE,kFAAkF,GAAG,OAAO,CAAC,CAAA;IAC7H,CAAC;CACF;AAPD,oDAOC;AAED,MAAa,cAAe,SAAQ,YAAY;IAC9C;;MAEE;IACF,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,gBAAgB,EAAE,gCAAgC,GAAG,OAAO,CAAC,CAAA;IACrE,CAAC;CACF;AAPD,wCAOC;AAED,MAAa,mBAAoB,SAAQ,YAAY;IACnD;;MAEE;IACF,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,qBAAqB,EAAE,oDAAoD,GAAG,OAAO,CAAC,CAAA;IAC9F,CAAC;CACF;AAPD,kDAOC;AAID,MAAa,qBAAsB,SAAQ,YAAY;IACrD;;OAEG;IACH,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,uBAAuB,EAAE,oDAAoD,GAAG,OAAO,CAAC,CAAA;IAChG,CAAC;CACF;AAPD,sDAOC;AAED,MAAa,aAAc,SAAQ,YAAY;IAC7C;;OAEG;IACH,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,eAAe,EAAE,oCAAoC,GAAG,OAAO,CAAC,CAAA;IACxE,CAAC;CACF;AAPD,sCAOC;AAED,MAAa,cAAe,SAAQ,YAAY;IAC9C;;OAEG;IACH,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,gBAAgB,EAAE,yCAAyC,GAAG,OAAO,CAAC,CAAA;IAC9E,CAAC;CACF;AAPD,wCAOC;AAED,MAAa,WAAY,SAAQ,YAAY;IAC3C;;OAEG;IACH,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,aAAa,EAAE,iDAAiD,GAAG,OAAO,CAAC,CAAA;IACnF,CAAC;CACF;AAPD,kCAOC;AAED,MAAa,qBAAsB,SAAQ,YAAY;IACrD;;OAEG;IACH,YAAY,UAAkB,EAAE;QAC9B,KAAK,CAAC,uBAAuB,EAAE,0CAA0C,GAAG,OAAO,CAAC,CAAA;IACtF,CAAC;CACF;AAPD,sDAOC"}