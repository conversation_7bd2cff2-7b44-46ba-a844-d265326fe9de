{"version": 3, "file": "DOMTokenListImpl.js", "sourceRoot": "", "sources": ["../../src/dom/DOMTokenListImpl.ts"], "names": [], "mappings": ";;AAAA,yBAAwB;AACxB,iDAAmE;AAEnE,2CAA8E;AAC9E,4CAIqB;AAErB;;GAEG;AACH,MAAa,gBAAgB;IAM3B;;;;;OAKG;IACH,YAAoB,OAAgB,EAAE,SAAe;QAEnD;;;;;;;WAOG;QACH,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAA;QACvB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAA;QAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAA;QAE1B,MAAM,SAAS,GAAG,SAAS,CAAC,UAAU,CAAA;QACtC,MAAM,KAAK,GAAG,uCAA2B,CAAC,OAAO,EAAE,SAAS,CAAC,CAAA;QAE7D,8EAA8E;QAC9E,MAAM,OAAO,GAAG,IAAI,CAAA;QACpB,SAAS,cAAc,CAAC,OAAgB,EAAE,SAAiB,EACzD,QAAuB,EAAE,KAAoB,EAAE,SAAwB;YACvE;;;;;eAKG;YACH,IAAI,SAAS,KAAK,OAAO,CAAC,UAAU,CAAC,UAAU,IAAI,SAAS,KAAK,IAAI,EAAE;gBACrE,IAAI,CAAC,KAAK;oBACR,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,CAAA;;oBAEzB,OAAO,CAAC,SAAS,GAAG,4BAAgB,CAAC,KAAK,CAAC,CAAA;aAC9C;QACH,CAAC;QACD,qEAAqE;QACrE,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QAExD,IAAI,MAAG,CAAC,QAAQ,CAAC,KAAK,EAAE;YACtB,uCAA2B,CAAC,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;SACpE;IACH,CAAC;IAED,kBAAkB;IAClB,IAAI,MAAM;QACR;;;WAGG;QACH,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAA;IAC5B,CAAC;IAED,kBAAkB;IAClB,IAAI,CAAC,KAAa;QAChB;;;;WAIG;QACH,IAAI,CAAC,GAAG,CAAC,CAAA;QACT,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE;YAClC,IAAI,CAAC,KAAK,KAAK;gBAAE,OAAO,KAAK,CAAA;YAC7B,CAAC,EAAE,CAAA;SACJ;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,QAAQ,CAAC,KAAa;QACpB;;;WAGG;QACH,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;IAClC,CAAC;IAED,kBAAkB;IAClB,GAAG,CAAC,GAAG,MAAgB;QACrB;;;;;;;;WAQG;QACH,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC1B,IAAI,KAAK,KAAK,EAAE,EAAE;gBAChB,MAAM,IAAI,0BAAW,CAAC,4BAA4B,CAAC,CAAA;aACpD;iBAAM,IAAI,iBAAc,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBACrD,MAAM,IAAI,oCAAqB,CAAC,kCAAkC,CAAC,CAAA;aACpE;iBAAM;gBACL,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;aAC1B;SACF;QACD,iCAAqB,CAAC,IAAI,CAAC,CAAA;IAC7B,CAAC;IAED,kBAAkB;IAClB,MAAM,CAAC,GAAG,MAAgB;QACxB;;;;;;;;WAQG;QACH,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC1B,IAAI,KAAK,KAAK,EAAE,EAAE;gBAChB,MAAM,IAAI,0BAAW,CAAC,+BAA+B,CAAC,CAAA;aACvD;iBAAM,IAAI,iBAAc,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBACrD,MAAM,IAAI,oCAAqB,CAAC,kCAAkC,CAAC,CAAA;aACpE;iBAAM;gBACL,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;aAC7B;SACF;QACD,iCAAqB,CAAC,IAAI,CAAC,CAAA;IAC7B,CAAC;IAED,kBAAkB;IAClB,MAAM,CAAC,KAAa,EAAE,QAA6B,SAAS;QAC1D;;;;WAIG;QACH,IAAI,KAAK,KAAK,EAAE,EAAE;YAChB,MAAM,IAAI,0BAAW,CAAC,+BAA+B,CAAC,CAAA;SACvD;aAAM,IAAI,iBAAc,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACrD,MAAM,IAAI,oCAAqB,CAAC,kCAAkC,CAAC,CAAA;SACpE;QAED;;WAEG;QACH,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAC7B;;;;eAIG;YACH,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,KAAK,EAAE;gBAC1C,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;gBAC5B,iCAAqB,CAAC,IAAI,CAAC,CAAA;gBAC3B,OAAO,KAAK,CAAA;aACb;YAED,OAAO,IAAI,CAAA;SACZ;QAED;;;WAGG;QACH,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE;YACzC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;YACzB,iCAAqB,CAAC,IAAI,CAAC,CAAA;YAC3B,OAAO,IAAI,CAAA;SACZ;QAED;;WAEG;QACH,OAAO,KAAK,CAAA;IACd,CAAC;IAED,kBAAkB;IAClB,OAAO,CAAC,KAAa,EAAE,QAAgB;QACrC;;;;;WAKG;QACH,IAAI,KAAK,KAAK,EAAE,IAAI,QAAQ,KAAK,EAAE,EAAE;YACnC,MAAM,IAAI,0BAAW,CAAC,gCAAgC,CAAC,CAAA;SACxD;aAAM,IAAI,iBAAc,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,iBAAc,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YACtG,MAAM,IAAI,oCAAqB,CAAC,kCAAkC,CAAC,CAAA;SACpE;QAED;;;WAGG;QACH,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC;YAAE,OAAO,KAAK,CAAA;QAE5C;;;;WAIG;QACH,WAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAA;QACjD,iCAAqB,CAAC,IAAI,CAAC,CAAA;QAC3B,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,QAAQ,CAAC,KAAa;QACpB;;;WAGG;QACH,OAAO,qCAAyB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;IAC/C,CAAC;IAED,kBAAkB;IAClB,IAAI,KAAK;QACP;;;WAGG;QACH,OAAO,oCAAwB,CAAC,IAAI,CAAC,CAAA;IACvC,CAAC;IACD,IAAI,KAAK,CAAC,KAAa;QACrB;;;;WAIG;QACH,uCAA2B,CAAC,IAAI,CAAC,QAAQ,EACvC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA;IACtC,CAAC;IAED;;OAEG;IACH,CAAC,MAAM,CAAC,QAAQ,CAAC;QACf,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAA;QAE5C,OAAO;YACL,IAAI;gBACF,OAAO,EAAE,CAAC,IAAI,EAAE,CAAA;YAClB,CAAC;SACF,CAAA;IACH,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,OAAO,CAAC,OAAgB,EAAE,SAAe;QAC9C,OAAO,IAAI,gBAAgB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAA;IACjD,CAAC;CAEF;AApQD,4CAoQC"}