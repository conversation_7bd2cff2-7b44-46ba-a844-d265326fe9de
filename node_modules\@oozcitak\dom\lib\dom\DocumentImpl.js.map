{"version": 3, "file": "DocumentImpl.js", "sourceRoot": "", "sources": ["../../src/dom/DocumentImpl.ts"], "names": [], "mappings": ";;AAAA,yBAAwB;AACxB,6CAKqB;AACrB,iDAEuB;AACvB,yCAAqC;AACrC,kCAA+B;AAC/B,yCAAqD;AACrD,2CAA6D;AAC7D,iEAA8D;AAC9D,4CASqB;AACrB,kEAA8D;AAE9D;;GAEG;AACH,MAAa,YAAa,SAAQ,mBAAQ;IAkCxC;;OAEG;IACH;QACE,KAAK,EAAE,CAAA;QAnCT,cAAS,GAAG,IAAI,GAAG,EAAQ,CAAA;QAE3B,cAAS,GAAG;YACV,IAAI,EAAE,OAAO;YACb,MAAM,EAAE,CAAC,mBAAmB,EAAE,OAAO,EAAE,MAAM,CAAC;SAC/C,CAAA;QACD,iBAAY,GAAG,iBAAiB,CAAA;QAChC,SAAI,GAAG;YACL,MAAM,EAAE,OAAO;YACf,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,EAAE;YACZ,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,CAAC,OAAO,CAAC;YACf,KAAK,EAAE,IAAI;YACX,QAAQ,EAAE,IAAI;YACd,qBAAqB,EAAE,IAAI;YAC3B,aAAa,EAAE,IAAI;SACpB,CAAA;QACD,YAAO,GAAG,IAAI,CAAA;QACd,UAAK,GAAmB,KAAK,CAAA;QAC7B,UAAK,GAA8C,WAAW,CAAA;QAG9D,qBAAgB,GAAG,IAAI,CAAA;QACvB,mBAAc,GAAG,KAAK,CAAA;QAEtB,2BAAsB,GAAoB,IAAI,CAAA;IAS9C,CAAC;IARD,IAAI,aAAa,KAAe,OAAO,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAA,CAAC,CAAC;IAC5E,IAAI,aAAa,CAAC,GAAa,IAAI,IAAI,CAAC,sBAAsB,GAAG,GAAG,CAAA,CAAC,CAAC;IAStE,kBAAkB;IAClB,IAAI,cAAc;QAChB;;;WAGG;QACH,OAAO,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,oCAAwB,CAAC,IAAI,CAAC,CAAC,CAAA;IACxF,CAAC;IAED,kBAAkB;IAClB,IAAI,GAAG;QACL;;;;WAIG;QACH,OAAO,4BAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACjC,CAAC;IAED,kBAAkB;IAClB,IAAI,WAAW,KAAa,OAAO,IAAI,CAAC,GAAG,CAAA,CAAC,CAAC;IAE7C,kBAAkB;IAClB,IAAI,MAAM;QACR,OAAO,MAAM,CAAA;IACf,CAAC;IAED,kBAAkB;IAClB,IAAI,UAAU;QACZ;;;WAGG;QACH,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAA;IAC9D,CAAC;IAED,kBAAkB;IAClB,IAAI,YAAY;QACd;;;;WAIG;QACH,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAA;IAC5B,CAAC;IAED,kBAAkB;IAClB,IAAI,OAAO,KAAa,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAA,CAAC,CAAC;IAEpD,kBAAkB;IAClB,IAAI,aAAa,KAAa,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAA,CAAC,CAAC;IAE1D,kBAAkB;IAClB,IAAI,WAAW;QACb;;WAEG;QACH,OAAO,IAAI,CAAC,YAAY,CAAA;IAC1B,CAAC;IAED,kBAAkB;IAClB,IAAI,OAAO;QACT;;;WAGG;QACH,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE;YAClC,IAAI,YAAK,CAAC,kBAAkB,CAAC,KAAK,CAAC;gBACjC,OAAO,KAAK,CAAA;SACf;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,IAAI,eAAe;QACjB;;WAEG;QACH,OAAO,IAAI,CAAC,gBAAgB,CAAA;IAC9B,CAAC;IAED,kBAAkB;IAClB,oBAAoB,CAAC,aAAqB;QACxC;;;WAGG;QACH,OAAO,gDAAoC,CAAC,aAAa,EAAE,IAAI,CAAC,CAAA;IAClE,CAAC;IAED,kBAAkB;IAClB,sBAAsB,CAAC,SAAwB,EAAE,SAAiB;QAChE;;;;WAIG;QACH,OAAO,4CAAgC,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;IACrE,CAAC;IAED,kBAAkB;IAClB,sBAAsB,CAAC,UAAkB;QACvC;;;WAGG;QACH,OAAO,6CAAiC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;IAC5D,CAAC;IAED,kBAAkB;IAClB,aAAa,CAAC,SAAiB,EAAE,OAAiC;QAChE;;;;;;;;;;;;;;WAcG;QAEH,IAAI,CAAC,sBAAU,CAAC,SAAS,CAAC;YACxB,MAAM,IAAI,oCAAqB,EAAE,CAAA;QAEnC,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM;YAAE,SAAS,GAAG,SAAS,CAAC,WAAW,EAAE,CAAA;QAE9D,IAAI,EAAE,GAAkB,IAAI,CAAA;QAC5B,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,IAAI,eAAQ,CAAC,OAAO,CAAC,EAAE;gBACrB,EAAE,GAAG,OAAO,CAAA;aACb;iBAAM;gBACL,EAAE,GAAG,OAAO,CAAC,EAAE,CAAA;aAChB;SACF;QAED,MAAM,SAAS,GACb,CAAC,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,IAAI,CAAC,YAAY,KAAK,uBAAuB,CAAC,CAAC,CAAC;YACxE,iBAAc,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAA;QAE9B,OAAO,mCAAuB,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAC7D,EAAE,EAAE,IAAI,CAAC,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,eAAe,CAAC,SAAwB,EAAE,aAAqB,EAC7D,OAAiC;QACjC;;;;WAIG;QACH,OAAO,4CAAgC,CAAC,IAAI,EAAE,SAAS,EACrD,aAAa,EAAE,OAAO,CAAC,CAAA;IAC3B,CAAC;IAED,kBAAkB;IAClB,sBAAsB;QACpB;;;WAGG;QACH,OAAO,mCAAuB,CAAC,IAAI,CAAC,CAAA;IACtC,CAAC;IAED,kBAAkB;IAClB,cAAc,CAAC,IAAY;QACzB;;;WAGG;QACH,OAAO,uBAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAChC,CAAC;IAED,kBAAkB;IAClB,kBAAkB,CAAC,IAAY;QAC7B;;;;;;;WAOG;QACH,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM;YACvB,MAAM,IAAI,gCAAiB,EAAE,CAAA;QAE/B,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC5B,MAAM,IAAI,oCAAqB,EAAE,CAAA;QAEnC,OAAO,+BAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IACxC,CAAC;IAED,kBAAkB;IAClB,aAAa,CAAC,IAAY;QACxB;;;WAGG;QACH,OAAO,0BAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IACnC,CAAC;IAED,kBAAkB;IAClB,2BAA2B,CAAC,MAAc,EAAE,IAAY;QACtD;;;;;;;WAOG;QAEH,IAAI,CAAC,sBAAU,CAAC,MAAM,CAAC;YACrB,MAAM,IAAI,oCAAqB,EAAE,CAAA;QAEnC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3B,MAAM,IAAI,oCAAqB,EAAE,CAAA;QAEnC,OAAO,wCAA4B,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;IACzD,CAAC;IAED,kBAAkB;IAClB,UAAU,CAAC,IAAU,EAAE,OAAgB,KAAK;QAC1C;;WAEG;QACH,IAAI,YAAK,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,YAAK,CAAC,YAAY,CAAC,IAAI,CAAC;YACxD,MAAM,IAAI,gCAAiB,EAAE,CAAA;QAE/B;;WAEG;QACH,OAAO,sBAAU,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;IACrC,CAAC;IAED,kBAAkB;IAClB,SAAS,CAAC,IAAU;QAClB;;WAEG;QACH,IAAI,YAAK,CAAC,cAAc,CAAC,IAAI,CAAC;YAC5B,MAAM,IAAI,gCAAiB,EAAE,CAAA;QAE/B;;WAEG;QACH,IAAI,YAAK,CAAC,YAAY,CAAC,IAAI,CAAC;YAC1B,MAAM,IAAI,oCAAqB,EAAE,CAAA;QAEnC;;;WAGG;QACH,0BAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QAC1B,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,eAAe,CAAC,SAAiB;QAC/B;;;;;;;WAOG;QACH,IAAI,CAAC,sBAAU,CAAC,SAAS,CAAC;YACxB,MAAM,IAAI,oCAAqB,EAAE,CAAA;QAEnC,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE;YACzB,SAAS,GAAG,SAAS,CAAC,WAAW,EAAE,CAAA;SACpC;QAED,MAAM,IAAI,GAAG,uBAAW,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;QACzC,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,iBAAiB,CAAC,SAAiB,EAAE,aAAqB;QAExD;;;;;WAKG;QACH,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,CAAC,GAAG,wCAA4B,CAC1D,SAAS,EAAE,aAAa,CAAC,CAAA;QAE3B,MAAM,IAAI,GAAG,uBAAW,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;QACzC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAA;QACpB,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAA;QAC9B,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,WAAW,CAAC,cAAsB;QAChC,OAAO,mCAAuB,CAAC,cAAc,CAAC,CAAA;IAChD,CAAC;IAED,kBAAkB;IAClB,WAAW;QACT;;;WAGG;QACH,MAAM,KAAK,GAAG,wBAAY,EAAE,CAAA;QAC5B,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;QACxB,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;QACtB,OAAO,KAAK,CAAA;IACd,CAAC;IAED,kBAAkB;IAClB,kBAAkB,CAAC,IAAU,EAAE,aAAyB,uBAAU,CAAC,GAAG,EACpE,SAA6D,IAAI;QAEjE;;;;;;;WAOG;QACH,MAAM,QAAQ,GAAG,+BAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;QACtD,QAAQ,CAAC,WAAW,GAAG,UAAU,CAAA;QACjC,QAAQ,CAAC,mBAAmB,GAAG,2BAAe,CAAC,IAAI,CAAC,CAAA;QACpD,IAAI,iBAAU,CAAC,MAAM,CAAC,EAAE;YACtB,QAAQ,CAAC,OAAO,GAAG,6BAAiB,EAAE,CAAA;YACtC,QAAQ,CAAC,OAAO,CAAC,UAAU,GAAG,MAAM,CAAA;SACrC;aAAM;YACL,QAAQ,CAAC,OAAO,GAAG,MAAM,CAAA;SAC1B;QACD,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,kBAAkB;IAClB,gBAAgB,CAAC,IAAU,EAAE,aAAyB,uBAAU,CAAC,GAAG,EAClE,SAA6D,IAAI;QACjE;;;;;;WAMG;QACH,MAAM,MAAM,GAAG,6BAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QAC5C,MAAM,CAAC,WAAW,GAAG,UAAU,CAAA;QAC/B,IAAI,iBAAU,CAAC,MAAM,CAAC,EAAE;YACtB,MAAM,CAAC,OAAO,GAAG,6BAAiB,EAAE,CAAA;YACpC,MAAM,CAAC,OAAO,CAAC,UAAU,GAAG,MAAM,CAAA;SACnC;aAAM;YACL,MAAM,CAAC,OAAO,GAAG,MAAM,CAAA;SACxB;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,aAAa,CAAC,KAAY;QACxB;;;;;WAKG;QACH,IAAI,KAAK,CAAC,KAAK,KAAK,MAAM,EAAE;YAC1B,OAAO,IAAI,CAAA;SACZ;aAAM;YACL,OAAO,MAAG,CAAC,MAAM,CAAA;SAClB;IACH,CAAC;IAED,8BAA8B;IAC9B,0BAA0B;IAC1B,cAAc,CAAC,SAAiB,IAAoB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAA,CAAC,CAAC;IAErH,8BAA8B;IAC9B,cAAc;IAEd,oBAAoB;IACpB,0BAA0B;IAC1B,IAAI,QAAQ,KAAqB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA,CAAC,CAAC;IACxF,0BAA0B;IAC1B,IAAI,iBAAiB,KAAqB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA,CAAC,CAAC;IACjG,0BAA0B;IAC1B,IAAI,gBAAgB,KAAqB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA,CAAC,CAAC;IAChG,0BAA0B;IAC1B,IAAI,iBAAiB,KAAa,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA,CAAC,CAAC;IACzF,0BAA0B;IAC1B,OAAO,CAAC,GAAG,KAAwB,IAAU,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA,CAAC,CAAC;IACpG,0BAA0B;IAC1B,MAAM,CAAC,GAAG,KAAwB,IAAU,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA,CAAC,CAAC;IACnG,0BAA0B;IAC1B,aAAa,CAAC,SAAiB,IAAoB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA,CAAC,CAAC;IAC1G,0BAA0B;IAC1B,gBAAgB,CAAC,SAAiB,IAAc,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA,CAAC,CAAC;CAExG;AAjcD,oCAicC;AAED;;GAEG;AACH,iCAAe,CAAC,YAAY,CAAC,SAAS,EAAE,WAAW,EAAE,qBAAQ,CAAC,QAAQ,CAAC,CAAA"}