"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * Represents a mixin for an interface to be used to share APIs between
 * documents and shadow roots. This mixin is implemented by
 * {@link Document} and {@link ShadowRoot}.
 *
 * _Note:_ The DocumentOrShadowRoot mixin is expected to be used by other
 * standards that want to define APIs shared between documents and shadow roots.
 */
class DocumentOrShadowRootImpl {
}
exports.DocumentOrShadowRootImpl = DocumentOrShadowRootImpl;
//# sourceMappingURL=DocumentOrShadowRootImpl.js.map