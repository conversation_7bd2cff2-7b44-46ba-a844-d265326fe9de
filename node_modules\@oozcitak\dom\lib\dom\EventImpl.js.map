{"version": 3, "file": "EventImpl.js", "sourceRoot": "", "sources": ["../../src/dom/EventImpl.ts"], "names": [], "mappings": ";;AAAA,6CAEqB;AACrB,4CAAyE;AACzE,kEAA8D;AAE9D;;GAEG;AACH,MAAa,SAAS;IAkCpB;;OAEG;IACH,YAAmB,IAAY,EAAE,SAAqB;QAzBtD,YAAO,GAAyB,IAAI,CAAA;QACpC,mBAAc,GAAyB,IAAI,CAAA;QAC3C,qBAAgB,GAA2B,EAAE,CAAA;QAC7C,UAAK,GAAoB,EAAE,CAAA;QAC3B,mBAAc,GAAyB,IAAI,CAAA;QAC3C,gBAAW,GAAe,uBAAU,CAAC,IAAI,CAAA;QAEzC,yBAAoB,GAAY,KAAK,CAAA;QACrC,kCAA6B,GAAY,KAAK,CAAA;QAC9C,kBAAa,GAAY,KAAK,CAAA;QAC9B,2BAAsB,GAAY,KAAK,CAAA;QACvC,kBAAa,GAAY,KAAK,CAAA;QAC9B,qBAAgB,GAAY,KAAK,CAAA;QACjC,kBAAa,GAAY,KAAK,CAAA;QAE9B,eAAU,GAAY,KAAK,CAAA;QAG3B,aAAQ,GAAY,KAAK,CAAA;QACzB,gBAAW,GAAY,KAAK,CAAA;QAQ1B;;;;;;;;WAQG;QACH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;QACjB,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,OAAO,IAAI,KAAK,CAAA;YAC1C,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,UAAU,IAAI,KAAK,CAAA;YAChD,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,QAAQ,IAAI,KAAK,CAAA;SACjD;QACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAA;QAC5B,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAA;IACxC,CAAC;IAED,kBAAkB;IAClB,IAAI,IAAI,KAAa,OAAO,IAAI,CAAC,KAAK,CAAA,CAAC,CAAC;IAExC,kBAAkB;IAClB,IAAI,MAAM,KAAyB,OAAO,IAAI,CAAC,OAAO,CAAA,CAAC,CAAC;IAExD,kBAAkB;IAClB,IAAI,UAAU,KAAyB,OAAO,IAAI,CAAC,OAAO,CAAA,CAAC,CAAC;IAE5D,kBAAkB;IAClB,IAAI,aAAa,KAAyB,OAAO,IAAI,CAAC,cAAc,CAAA,CAAC,CAAC;IAEtE,kBAAkB;IAClB,YAAY;QAEV;;;;;;;;;WASG;QACH,MAAM,YAAY,GAAkB,EAAE,CAAA;QAEtC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAA;QAEvB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,YAAY,CAAA;QAE1C,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAA;QACzC,IAAI,aAAa,KAAK,IAAI,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;SAChD;QACD,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;QAEhC,IAAI,kBAAkB,GAAG,CAAC,CAAA;QAC1B,IAAI,+BAA+B,GAAG,CAAC,CAAA;QAEvC;;;WAGG;QACH,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;QAC3B,OAAO,KAAK,IAAI,CAAC,EAAE;YACjB;;;;;;;;eAQG;YACH,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE;gBAChC,+BAA+B,EAAE,CAAA;aAClC;YACD,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,KAAK,aAAa,EAAE;gBAClD,kBAAkB,GAAG,KAAK,CAAA;gBAC1B,MAAK;aACN;YACD,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE;gBAChC,+BAA+B,EAAE,CAAA;aAClC;YACD,KAAK,EAAE,CAAA;SACR;QAED;;;WAGG;QACH,IAAI,kBAAkB,GAAG,+BAA+B,CAAA;QACxD,IAAI,cAAc,GAAG,+BAA+B,CAAA;QAEpD;;;WAGG;QACH,KAAK,GAAG,kBAAkB,GAAG,CAAC,CAAA;QAC9B,OAAO,KAAK,IAAI,CAAC,EAAE;YACjB;;;;;eAKG;YACH,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE;gBAChC,kBAAkB,EAAE,CAAA;aACrB;YAED,IAAI,kBAAkB,IAAI,cAAc,EAAE;gBACxC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,CAAC,CAAA;aACnD;YAED;;eAEG;YACH,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE;gBAChC;;;;mBAIG;gBACH,kBAAkB,EAAE,CAAA;gBACpB,IAAI,kBAAkB,GAAG,cAAc,EAAE;oBACvC,cAAc,GAAG,kBAAkB,CAAA;iBACpC;aACF;YAED;;eAEG;YACH,KAAK,EAAE,CAAA;SACR;QAED;;;WAGG;QACH,kBAAkB,GAAG,+BAA+B,CAAA;QACpD,cAAc,GAAG,+BAA+B,CAAA;QAEhD;;;WAGG;QACH,KAAK,GAAG,kBAAkB,GAAG,CAAC,CAAA;QAC9B,OAAO,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE;YAC1B;;;;;eAKG;YACH,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE;gBAChC,kBAAkB,EAAE,CAAA;aACrB;YAED,IAAI,kBAAkB,IAAI,cAAc,EAAE;gBACxC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,CAAC,CAAA;aAChD;YAED;;eAEG;YACH,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE;gBAChC;;;;mBAIG;gBACH,kBAAkB,EAAE,CAAA;gBACpB,IAAI,kBAAkB,GAAG,cAAc,EAAE;oBACvC,cAAc,GAAG,kBAAkB,CAAA;iBACpC;aACF;YAED;;eAEG;YACH,KAAK,EAAE,CAAA;SACR;QAED;;WAEG;QACH,OAAO,YAAY,CAAA;IACrB,CAAC;IAED,kBAAkB;IAClB,IAAI,UAAU,KAAiB,OAAO,IAAI,CAAC,WAAW,CAAA,CAAC,CAAC;IAExD,kBAAkB;IAClB,eAAe,KAAW,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAA,CAAC,CAAC;IAE5D,kBAAkB;IAClB,IAAI,YAAY,KAAc,OAAO,IAAI,CAAC,oBAAoB,CAAA,CAAC,CAAC;IAChE,IAAI,YAAY,CAAC,KAAc,IAAI,IAAI,KAAK;QAAE,IAAI,CAAC,eAAe,EAAE,CAAA,CAAC,CAAC;IAEtE,kBAAkB;IAClB,wBAAwB;QACtB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAA;QAChC,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAA;IAC3C,CAAC;IAED,kBAAkB;IAClB,IAAI,OAAO,KAAc,OAAO,IAAI,CAAC,QAAQ,CAAA,CAAC,CAAC;IAE/C,kBAAkB;IAClB,IAAI,UAAU,KAAc,OAAO,IAAI,CAAC,WAAW,CAAA,CAAC,CAAC;IAErD,kBAAkB;IAClB,IAAI,WAAW,KAAc,OAAO,CAAC,IAAI,CAAC,aAAa,CAAA,CAAC,CAAC;IACzD,IAAI,WAAW,CAAC,KAAc;QAC5B,IAAI,CAAC,KAAK,EAAE;YACV,oCAAwB,CAAC,IAAI,CAAC,CAAA;SAC/B;IACH,CAAC;IAED,kBAAkB;IAClB,cAAc;QACZ,oCAAwB,CAAC,IAAI,CAAC,CAAA;IAChC,CAAC;IAED,kBAAkB;IAClB,IAAI,gBAAgB,KAAc,OAAO,IAAI,CAAC,aAAa,CAAA,CAAC,CAAC;IAE7D,kBAAkB;IAClB,IAAI,QAAQ,KAAc,OAAO,IAAI,CAAC,aAAa,CAAA,CAAC,CAAC;IAErD,kBAAkB;IAClB,IAAI,SAAS,KAAc,OAAO,IAAI,CAAC,UAAU,CAAA,CAAC,CAAC;IAEnD,kBAAkB;IAClB,IAAI,SAAS,KAAa,OAAO,IAAI,CAAC,UAAU,CAAA,CAAC,CAAC;IAElD,kBAAkB;IAClB,SAAS,CAAC,IAAY,EAAE,OAAO,GAAG,KAAK,EAAE,UAAU,GAAG,KAAK;QACzD;;WAEG;QACH,IAAI,IAAI,CAAC,aAAa;YAAE,OAAM;QAE9B;;WAEG;QACH,4BAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,CAAC,CAAA;IACnD,CAAC;;AA9RH,8BAgSC;AA9RQ,cAAI,GAAG,CAAC,CAAA;AACR,yBAAe,GAAG,CAAC,CAAA;AACnB,mBAAS,GAAG,CAAC,CAAA;AACb,wBAAc,GAAG,CAAC,CAAA;AA6R3B;;GAEG;AACH,iCAAe,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,EAAG,CAAC,CAAC,CAAA;AAChD,iCAAe,CAAC,SAAS,CAAC,SAAS,EAAE,iBAAiB,EAAG,CAAC,CAAC,CAAA;AAC3D,iCAAe,CAAC,SAAS,CAAC,SAAS,EAAE,WAAW,EAAG,CAAC,CAAC,CAAA;AACrD,iCAAe,CAAC,SAAS,CAAC,SAAS,EAAE,gBAAgB,EAAG,CAAC,CAAC,CAAA"}