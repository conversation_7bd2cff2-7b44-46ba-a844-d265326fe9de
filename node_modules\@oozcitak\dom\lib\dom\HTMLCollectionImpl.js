"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const infra_1 = require("@oozcitak/infra");
const algorithm_1 = require("../algorithm");
const util_1 = require("../util");
const util_2 = require("@oozcitak/util");
/**
 * Represents a collection of elements.
 */
class HTMLCollectionImpl {
    /**
     * Initializes a new instance of `HTMLCollection`.
     *
     * @param root - root node
     * @param filter - node filter
     */
    constructor(root, filter) {
        this._live = true;
        this._root = root;
        this._filter = filter;
        return new Proxy(this, this);
    }
    /** @inheritdoc */
    get length() {
        /**
         * The length attribute’s getter must return the number of nodes
         * represented by the collection.
         */
        let count = 0;
        let node = algorithm_1.tree_getFirstDescendantNode(this._root, false, false, (e) => util_1.Guard.isElementNode(e) && this._filter(e));
        while (node !== null) {
            count++;
            node = algorithm_1.tree_getNextDescendantNode(this._root, node, false, false, (e) => util_1.Guard.isElementNode(e) && this._filter(e));
        }
        return count;
    }
    /** @inheritdoc */
    item(index) {
        /**
         * The item(index) method, when invoked, must return the indexth element
         * in the collection. If there is no indexth element in the collection,
         * then the method must return null.
         */
        let i = 0;
        let node = algorithm_1.tree_getFirstDescendantNode(this._root, false, false, (e) => util_1.Guard.isElementNode(e) && this._filter(e));
        while (node !== null) {
            if (i === index)
                return node;
            else
                i++;
            node = algorithm_1.tree_getNextDescendantNode(this._root, node, false, false, (e) => util_1.Guard.isElementNode(e) && this._filter(e));
        }
        return null;
    }
    /** @inheritdoc */
    namedItem(key) {
        /**
         * 1. If key is the empty string, return null.
         * 2. Return the first element in the collection for which at least one of
         * the following is true:
         * - it has an ID which is key;
         * - it is in the HTML namespace and has a name attribute whose value is key;
         * or null if there is no such element.
         */
        if (key === '')
            return null;
        let ele = algorithm_1.tree_getFirstDescendantNode(this._root, false, false, (e) => util_1.Guard.isElementNode(e) && this._filter(e));
        while (ele != null) {
            if (ele._uniqueIdentifier === key) {
                return ele;
            }
            else if (ele._namespace === infra_1.namespace.HTML) {
                for (let i = 0; i < ele._attributeList.length; i++) {
                    const attr = ele._attributeList[i];
                    if (attr._localName === "name" && attr._namespace === null &&
                        attr._namespacePrefix === null && attr._value === key)
                        return ele;
                }
            }
            ele = algorithm_1.tree_getNextDescendantNode(this._root, ele, false, false, (e) => util_1.Guard.isElementNode(e) && this._filter(e));
        }
        return null;
    }
    /** @inheritdoc */
    [Symbol.iterator]() {
        const root = this._root;
        const filter = this._filter;
        let currentNode = algorithm_1.tree_getFirstDescendantNode(root, false, false, (e) => util_1.Guard.isElementNode(e) && filter(e));
        return {
            next() {
                if (currentNode === null) {
                    return { done: true, value: null };
                }
                else {
                    const result = { done: false, value: currentNode };
                    currentNode = algorithm_1.tree_getNextDescendantNode(root, currentNode, false, false, (e) => util_1.Guard.isElementNode(e) && filter(e));
                    return result;
                }
            }
        };
    }
    /**
     * Implements a proxy get trap to provide array-like access.
     */
    get(target, key, receiver) {
        if (!util_2.isString(key) || HTMLCollectionImpl.reservedNames.indexOf(key) !== -1) {
            return Reflect.get(target, key, receiver);
        }
        const index = Number(key);
        if (isNaN(index)) {
            return target.namedItem(key) || undefined;
        }
        else {
            return target.item(index) || undefined;
        }
    }
    /**
     * Implements a proxy set trap to provide array-like access.
     */
    set(target, key, value, receiver) {
        if (!util_2.isString(key) || HTMLCollectionImpl.reservedNames.indexOf(key) !== -1) {
            return Reflect.set(target, key, value, receiver);
        }
        const index = Number(key);
        const node = isNaN(index) ?
            target.namedItem(key) || undefined : target.item(index) || undefined;
        if (node && node._parent) {
            algorithm_1.mutation_replace(node, value, node._parent);
            return true;
        }
        else {
            return false;
        }
    }
    /**
     * Creates a new `HTMLCollection`.
     *
     * @param root - root node
     * @param filter - node filter
     */
    static _create(root, filter = (() => true)) {
        return new HTMLCollectionImpl(root, filter);
    }
}
exports.HTMLCollectionImpl = HTMLCollectionImpl;
HTMLCollectionImpl.reservedNames = ['_root', '_live', '_filter', 'length',
    'item', 'namedItem', 'get', 'set'];
//# sourceMappingURL=HTMLCollectionImpl.js.map