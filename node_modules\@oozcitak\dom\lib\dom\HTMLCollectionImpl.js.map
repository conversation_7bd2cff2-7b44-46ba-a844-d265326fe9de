{"version": 3, "file": "HTMLCollectionImpl.js", "sourceRoot": "", "sources": ["../../src/dom/HTMLCollectionImpl.ts"], "names": [], "mappings": ";;AACA,2CAA6D;AAC7D,4CAAwG;AACxG,kCAA+B;AAC/B,yCAAyC;AAEzC;;GAEG;AACH,MAAa,kBAAkB;IAS7B;;;;;OAKG;IACH,YAAoB,IAAU,EAAE,MAAuC;QAbvE,UAAK,GAAY,IAAI,CAAA;QAcnB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;QACjB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAA;QAErB,OAAO,IAAI,KAAK,CAAqB,IAAI,EAAE,IAAI,CAAC,CAAA;IAClD,CAAC;IAED,kBAAkB;IAClB,IAAI,MAAM;QACR;;;WAGG;QACH,IAAI,KAAK,GAAG,CAAC,CAAA;QACb,IAAI,IAAI,GAAG,uCAA2B,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAC7D,CAAC,CAAC,EAAE,EAAE,CAAC,YAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;QACnD,OAAO,IAAI,KAAK,IAAI,EAAE;YACpB,KAAK,EAAE,CAAA;YACP,IAAI,GAAG,sCAA0B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAC9D,CAAC,CAAC,EAAE,EAAE,CAAC,YAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;SACpD;QACD,OAAO,KAAK,CAAA;IACd,CAAC;IAED,kBAAkB;IAClB,IAAI,CAAC,KAAa;QAChB;;;;WAIG;QACH,IAAI,CAAC,GAAG,CAAC,CAAA;QACT,IAAI,IAAI,GAAG,uCAA2B,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAC7D,CAAC,CAAC,EAAE,EAAE,CAAC,YAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAmB,CAAA;QACrE,OAAO,IAAI,KAAK,IAAI,EAAE;YACpB,IAAI,CAAC,KAAK,KAAK;gBACb,OAAO,IAAI,CAAA;;gBAEX,CAAC,EAAE,CAAA;YAEL,IAAI,GAAG,sCAA0B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAC9D,CAAC,CAAC,EAAE,EAAE,CAAC,YAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAmB,CAAA;SACtE;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,SAAS,CAAC,GAAW;QACnB;;;;;;;WAOG;QACH,IAAI,GAAG,KAAK,EAAE;YAAE,OAAO,IAAI,CAAA;QAE3B,IAAI,GAAG,GAAG,uCAA2B,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAC5D,CAAC,CAAC,EAAE,EAAE,CAAC,YAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAmB,CAAA;QAErE,OAAO,GAAG,IAAI,IAAI,EAAE;YAClB,IAAI,GAAG,CAAC,iBAAiB,KAAK,GAAG,EAAE;gBACjC,OAAO,GAAG,CAAA;aACX;iBAAM,IAAI,GAAG,CAAC,UAAU,KAAK,iBAAc,CAAC,IAAI,EAAE;gBACjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAClD,MAAM,IAAI,GAAG,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;oBACnC,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI;wBACxD,IAAI,CAAC,gBAAgB,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG;wBACrD,OAAO,GAAG,CAAA;iBACb;aACF;YAED,GAAG,GAAG,sCAA0B,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAC5D,CAAC,CAAC,EAAE,EAAE,CAAC,YAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAmB,CAAA;SACtE;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,CAAC,MAAM,CAAC,QAAQ,CAAC;QACf,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAA;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;QAC3B,IAAI,WAAW,GAAmB,uCAA2B,CAAC,IAAI,EAChE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,YAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAmB,CAAA;QAE7E,OAAO;YACL,IAAI;gBACF,IAAI,WAAW,KAAK,IAAI,EAAE;oBACxB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;iBACnC;qBAAM;oBACL,MAAM,MAAM,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,CAAA;oBAClD,WAAW,GAAG,sCAA0B,CAAC,IAAI,EAAE,WAAW,EACxD,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,YAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAmB,CAAA;oBAC7E,OAAO,MAAM,CAAA;iBACd;YACH,CAAC;SACF,CAAA;IACH,CAAC;IAQD;;OAEG;IACH,GAAG,CAAC,MAAsB,EAAE,GAAgB,EAAE,QAAa;QACzD,IAAI,CAAC,eAAQ,CAAC,GAAG,CAAC,IAAI,kBAAkB,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;YAC1E,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAA;SAC1C;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;QACzB,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE;YAChB,OAAO,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,SAAS,CAAA;SAC1C;aAAM;YACL,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,SAAS,CAAA;SACvC;IACH,CAAC;IAED;;OAEG;IACH,GAAG,CAAC,MAAsB,EAAE,GAAgB,EAAE,KAAc,EAAE,QAAa;QACzE,IAAI,CAAC,eAAQ,CAAC,GAAG,CAAC,IAAI,kBAAkB,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;YAC1E,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAA;SACjD;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;QACzB,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YACzB,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,SAAS,CAAA;QAEtE,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;YACxB,4BAAgB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;YAC3C,OAAO,IAAI,CAAA;SACZ;aAAM;YACL,OAAO,KAAK,CAAA;SACb;IACH,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,OAAO,CAAC,IAAU,EACvB,SAA0C,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC;QACtD,OAAO,IAAI,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;IAC7C,CAAC;;AAxKH,gDAyKC;AAnKkB,gCAAa,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ;IACrE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA"}