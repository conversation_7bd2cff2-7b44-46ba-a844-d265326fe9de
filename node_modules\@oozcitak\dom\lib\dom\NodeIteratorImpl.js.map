{"version": 3, "file": "NodeIteratorImpl.js", "sourceRoot": "", "sources": ["../../src/dom/NodeIteratorImpl.ts"], "names": [], "mappings": ";;AACA,mDAA+C;AAC/C,4CAA+E;AAE/E;;;GAGG;AACH,MAAa,gBAAiB,SAAQ,6BAAa;IAMjD;;OAEG;IACH,YAAoB,IAAU,EAAE,SAAe,EAAE,sBAA+B;QAC9E,KAAK,CAAC,IAAI,CAAC,CAAA;QAEX,IAAI,CAAC,mBAAmB,GAAG,SAAkC,CAAA;QAC7D,IAAI,CAAC,UAAU,GAAG,SAAS,CAAA;QAC3B,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAA;QAErD,qCAAyB,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IACvC,CAAC;IAED,kBAAkB;IAClB,IAAI,aAAa,KAAW,OAAO,IAAI,CAAC,UAAU,CAAA,CAAC,CAAC;IAEpD,kBAAkB;IAClB,IAAI,0BAA0B,KAAc,OAAO,IAAI,CAAC,uBAAuB,CAAA,CAAC,CAAC;IAEjF,kBAAkB;IAClB,QAAQ;QACN;;;WAGG;QACH,OAAO,iCAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAC1C,CAAC;IAED,kBAAkB;IAClB,YAAY;QACV;;;WAGG;QACH,OAAO,iCAAqB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;IAC3C,CAAC;IAED,kBAAkB;IAClB,MAAM;QACJ;;;;WAIG;QACH,qCAAyB,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAC1C,CAAC;IAGD;;;;;;;OAOG;IACH,MAAM,CAAC,OAAO,CAAC,IAAU,EAAE,SAAe,EAAE,sBAA+B;QAEzE,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,SAAS,EAAE,sBAAsB,CAAC,CAAA;IACtE,CAAC;CACF;AAlED,4CAkEC"}