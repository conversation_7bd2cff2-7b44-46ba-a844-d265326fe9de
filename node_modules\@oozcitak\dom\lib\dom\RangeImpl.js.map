{"version": 3, "file": "RangeImpl.js", "sourceRoot": "", "sources": ["../../src/dom/RangeImpl.ts"], "names": [], "mappings": ";;AAAA,yBAAwB;AACxB,6CAGqB;AACrB,2DAAuD;AACvD,iDAGuB;AACvB,4CAOqB;AACrB,kEAA8D;AAC9D,kCAA+B;AAE/B;;GAEG;AACH,MAAa,SAAU,SAAQ,qCAAiB;IAe9C;;OAEG;IACH;QACE,KAAK,EAAE,CAAA;QAEP;;;WAGG;QACH,MAAM,GAAG,GAAG,MAAG,CAAC,MAAM,CAAC,mBAAmB,CAAA;QAC1C,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;QACtB,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;QAEpB,MAAG,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IACzB,CAAC;IAED,kBAAkB;IAClB,IAAI,uBAAuB;QACzB;;;;;WAKG;QACH,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QAC9B,OAAO,CAAC,6BAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,EAAE;YACxD,IAAI,SAAS,CAAC,OAAO,KAAK,IAAI,EAAE;gBAC9B,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAA;aACzC;YACD,SAAS,GAAG,SAAS,CAAC,OAAO,CAAA;SAC9B;QAED,OAAO,SAAS,CAAA;IAClB,CAAC;IAED,kBAAkB;IAClB,QAAQ,CAAC,IAAU,EAAE,MAAc;QACjC;;;WAGG;QACH,6BAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAA;IACvC,CAAC;IAED,kBAAkB;IAClB,MAAM,CAAC,IAAU,EAAE,MAAc;QAC/B;;;WAGG;QACH,2BAAe,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAA;IACrC,CAAC;IAED,kBAAkB;IAClB,cAAc,CAAC,IAAU;QACvB;;;;;WAKG;QACH,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;QACzB,IAAI,MAAM,KAAK,IAAI;YACjB,MAAM,IAAI,mCAAoB,EAAE,CAAA;QAElC,6BAAiB,CAAC,IAAI,EAAE,MAAM,EAC5B,sBAAU,CAAC,IAAI,CAAC,CAAC,CAAA;IACrB,CAAC;IAED,kBAAkB;IAClB,aAAa,CAAC,IAAU;QACtB;;;;;WAKG;QACH,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;QACzB,IAAI,MAAM,KAAK,IAAI;YACjB,MAAM,IAAI,mCAAoB,EAAE,CAAA;QAElC,6BAAiB,CAAC,IAAI,EAAE,MAAM,EAC5B,sBAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;IACzB,CAAC;IAED,kBAAkB;IAClB,YAAY,CAAC,IAAU;QACrB;;;;;WAKG;QACH,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;QACzB,IAAI,MAAM,KAAK,IAAI;YACjB,MAAM,IAAI,mCAAoB,EAAE,CAAA;QAElC,2BAAe,CAAC,IAAI,EAAE,MAAM,EAC1B,sBAAU,CAAC,IAAI,CAAC,CAAC,CAAA;IACrB,CAAC;IAED,kBAAkB;IAClB,WAAW,CAAC,IAAU;QACpB;;;;;WAKG;QACH,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;QACzB,IAAI,MAAM,KAAK,IAAI;YACjB,MAAM,IAAI,mCAAoB,EAAE,CAAA;QAElC,2BAAe,CAAC,IAAI,EAAE,MAAM,EAC1B,sBAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;IACzB,CAAC;IAED,kBAAkB;IAClB,QAAQ,CAAC,OAA6B;QACpC;;;WAGG;QACH,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAA;SACxB;aAAM;YACL,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAA;SACxB;IACH,CAAC;IAED,kBAAkB;IAClB,UAAU,CAAC,IAAU;QACnB;;;WAGG;QACH,wBAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAC1B,CAAC;IAED,kBAAkB;IAClB,kBAAkB,CAAC,IAAU;QAC3B;;;;;WAKG;QACH,IAAI,YAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAChC,MAAM,IAAI,mCAAoB,EAAE,CAAA;QAElC,MAAM,MAAM,GAAG,2BAAe,CAAC,IAAI,CAAC,CAAA;QACpC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;QACvB,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;IAC5B,CAAC;IAED,kBAAkB;IAClB,qBAAqB,CAAC,GAAiB,EAAE,WAAkB;QACzD;;;;;;;WAOG;QACH,IAAI,GAAG,KAAK,yBAAY,CAAC,YAAY,IAAI,GAAG,KAAK,yBAAY,CAAC,UAAU;YACtE,GAAG,KAAK,yBAAY,CAAC,QAAQ,IAAI,GAAG,KAAK,yBAAY,CAAC,UAAU;YAChE,MAAM,IAAI,gCAAiB,EAAE,CAAA;QAE/B;;;WAGG;QACH,IAAI,sBAAU,CAAC,IAAI,CAAC,KAAK,sBAAU,CAAC,WAAW,CAAC;YAC9C,MAAM,IAAI,iCAAkB,EAAE,CAAA;QAEhC;;;;;;;;;;;;;;WAcG;QACH,IAAI,SAAwB,CAAA;QAC5B,IAAI,UAAyB,CAAA;QAE7B,QAAQ,GAAG,EAAE;YACX,KAAK,yBAAY,CAAC,YAAY;gBAC5B,SAAS,GAAG,IAAI,CAAC,MAAM,CAAA;gBACvB,UAAU,GAAG,WAAW,CAAC,MAAM,CAAA;gBAC/B,MAAK;YACP,KAAK,yBAAY,CAAC,UAAU;gBAC1B,SAAS,GAAG,IAAI,CAAC,IAAI,CAAA;gBACrB,UAAU,GAAG,WAAW,CAAC,MAAM,CAAA;gBAC/B,MAAK;YACP,KAAK,yBAAY,CAAC,QAAQ;gBACxB,SAAS,GAAG,IAAI,CAAC,IAAI,CAAA;gBACrB,UAAU,GAAG,WAAW,CAAC,IAAI,CAAA;gBAC7B,MAAK;YACP,KAAK,yBAAY,CAAC,UAAU;gBAC1B,SAAS,GAAG,IAAI,CAAC,MAAM,CAAA;gBACvB,UAAU,GAAG,WAAW,CAAC,IAAI,CAAA;gBAC7B,MAAK;YACP,0BAA0B;YAC1B;gBACE,MAAM,IAAI,gCAAiB,EAAE,CAAA;SAChC;QAED;;;;;;;;WAQG;QACH,MAAM,QAAQ,GAAG,kCAAsB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAA;QAE9D,IAAI,QAAQ,KAAK,6BAAgB,CAAC,MAAM,EAAE;YACxC,OAAO,CAAC,CAAC,CAAA;SACV;aAAM,IAAI,QAAQ,KAAK,6BAAgB,CAAC,KAAK,EAAE;YAC9C,OAAO,CAAC,CAAA;SACT;aAAM;YACL,OAAO,CAAC,CAAA;SACT;IACH,CAAC;IAED,kBAAkB;IAClB,cAAc;QACZ;;;;;WAKG;QACH,IAAI,2BAAe,CAAC,IAAI,CAAC;YAAE,OAAM;QAEjC,MAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAA;QACzC,MAAM,mBAAmB,GAAG,IAAI,CAAC,YAAY,CAAA;QAC7C,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAA;QACrC,MAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAA;QAEzC;;;;;;WAMG;QACH,IAAI,iBAAiB,KAAK,eAAe;YACvC,YAAK,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,EAAE;YAC9C,qCAAyB,CAAC,iBAAiB,EACzC,mBAAmB,EAAE,iBAAiB,GAAG,mBAAmB,EAAE,EAAE,CAAC,CAAA;YACnE,OAAM;SACP;QAED;;;;WAIG;QACH,MAAM,aAAa,GAAW,EAAE,CAAA;QAChC,KAAK,MAAM,IAAI,IAAI,mCAAuB,CAAC,IAAI,CAAC,EAAE;YAChD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;YAC3B,IAAI,MAAM,KAAK,IAAI,IAAI,6BAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACtD,SAAQ;aACT;YACD,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SACzB;QAED,IAAI,OAAa,CAAA;QACjB,IAAI,SAAiB,CAAA;QAErB,IAAI,6BAAiB,CAAC,eAAe,EAAE,iBAAiB,EAAE,IAAI,CAAC,EAAE;YAC/D;;;;eAIG;YACH,OAAO,GAAG,iBAAiB,CAAA;YAC3B,SAAS,GAAG,mBAAmB,CAAA;SAChC;aAAM;YACL;;;;;;;eAOG;YACH,IAAI,aAAa,GAAG,iBAAiB,CAAA;YACrC,OAAO,aAAa,CAAC,OAAO,KAAK,IAAI;gBACnC,CAAC,6BAAiB,CAAC,eAAe,EAAE,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE;gBAClE,aAAa,GAAG,aAAa,CAAC,OAAO,CAAA;aACtC;YACD,0BAA0B;YAC1B,IAAI,aAAa,CAAC,OAAO,KAAK,IAAI,EAAE;gBAClC,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAA;aACxC;YACD,OAAO,GAAG,aAAa,CAAC,OAAO,CAAA;YAC/B,SAAS,GAAG,sBAAU,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;SAC1C;QAED;;;;;WAKG;QACH,IAAI,YAAK,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,EAAE;YAChD,qCAAyB,CAAC,iBAAiB,EACzC,mBAAmB,EACnB,2BAAe,CAAC,iBAAiB,CAAC,GAAG,mBAAmB,EAAE,EAAE,CAAC,CAAA;SAChE;QAED;;;WAGG;QACH,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE;YAChC,0BAA0B;YAC1B,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,2BAAe,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;aACpC;SACF;QAED;;;;WAIG;QACH,IAAI,YAAK,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAAE;YAC9C,qCAAyB,CAAC,eAAe,EACvC,CAAC,EAAE,iBAAiB,EAAE,EAAE,CAAC,CAAA;SAC5B;QAED;;WAEG;QACH,IAAI,CAAC,MAAM,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAA;QAClC,IAAI,CAAC,IAAI,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAA;IAClC,CAAC;IAED,kBAAkB;IAClB,eAAe;QACb;;;WAGG;QACH,OAAO,yBAAa,CAAC,IAAI,CAAC,CAAA;IAC5B,CAAC;IAED,kBAAkB;IAClB,aAAa;QACX;;;WAGG;QACH,OAAO,kCAAsB,CAAC,IAAI,CAAC,CAAA;IACrC,CAAC;IAED,kBAAkB;IAClB,UAAU,CAAC,IAAU;QACnB;;;WAGG;QACH,OAAO,wBAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IACjC,CAAC;IAED,kBAAkB;IAClB,gBAAgB,CAAC,SAAe;QAC9B;;;WAGG;QACH,KAAK,MAAM,IAAI,IAAI,4CAAgC,CAAC,IAAI,CAAC,EAAE;YACzD,IAAI,CAAC,YAAK,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;gBAC3B,MAAM,IAAI,gCAAiB,EAAE,CAAA;aAC9B;SACF;QAED;;;WAGG;QACH,IAAI,YAAK,CAAC,cAAc,CAAC,SAAS,CAAC;YACjC,YAAK,CAAC,kBAAkB,CAAC,SAAS,CAAC;YACnC,YAAK,CAAC,sBAAsB,CAAC,SAAS,CAAC,EAAE;YACzC,MAAM,IAAI,mCAAoB,EAAE,CAAA;SACjC;QAED;;WAEG;QACH,MAAM,QAAQ,GAAG,yBAAa,CAAC,IAAI,CAAC,CAAA;QAEpC;;WAEG;QACH,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,EAAE;YACpC,+BAAmB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;SACrC;QAED;;;WAGG;QACH,wBAAY,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;QAC7B,2BAAe,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAA;QAEpC;;WAEG;QACH,wBAAY,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;IAC/B,CAAC;IAED,kBAAkB;IAClB,UAAU;QACR;;;WAGG;QACH,OAAO,wBAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;IAC7C,CAAC;IAED,kBAAkB;IAClB,MAAM;QACJ;;;;WAIG;QACH,MAAG,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAC5B,CAAC;IAED,kBAAkB;IAClB,cAAc,CAAC,IAAU,EAAE,MAAc;QACvC;;WAEG;QACH,IAAI,yBAAa,CAAC,IAAI,CAAC,KAAK,sBAAU,CAAC,IAAI,CAAC,EAAE;YAC5C,OAAO,KAAK,CAAA;SACb;QAED;;;;WAIG;QACH,IAAI,YAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAChC,MAAM,IAAI,mCAAoB,EAAE,CAAA;QAClC,IAAI,MAAM,GAAG,2BAAe,CAAC,IAAI,CAAC;YAChC,MAAM,IAAI,6BAAc,EAAE,CAAA;QAE5B;;WAEG;QACH,MAAM,EAAE,GAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;QACxC,IAAI,kCAAsB,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,6BAAgB,CAAC,MAAM;YACrE,kCAAsB,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,6BAAgB,CAAC,KAAK,EAAE;YAClE,OAAO,KAAK,CAAA;SACb;QAED;;WAEG;QACH,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,YAAY,CAAC,IAAU,EAAE,MAAc;QACrC;;;;;;WAMG;QACH,IAAI,yBAAa,CAAC,IAAI,CAAC,KAAK,sBAAU,CAAC,IAAI,CAAC;YAC1C,MAAM,IAAI,iCAAkB,EAAE,CAAA;QAChC,IAAI,YAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAChC,MAAM,IAAI,mCAAoB,EAAE,CAAA;QAClC,IAAI,MAAM,GAAG,2BAAe,CAAC,IAAI,CAAC;YAChC,MAAM,IAAI,6BAAc,EAAE,CAAA;QAE5B;;;;WAIG;QACH,MAAM,EAAE,GAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;QACxC,IAAI,kCAAsB,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,6BAAgB,CAAC,MAAM,EAAE;YACvE,OAAO,CAAC,CAAC,CAAA;SACV;aAAM,IAAI,kCAAsB,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,6BAAgB,CAAC,KAAK,EAAE;YAC3E,OAAO,CAAC,CAAA;SACT;aAAM;YACL,OAAO,CAAC,CAAA;SACT;IACH,CAAC;IAED,kBAAkB;IAClB,cAAc,CAAC,IAAU;QACvB;;WAEG;QACH,IAAI,yBAAa,CAAC,IAAI,CAAC,KAAK,sBAAU,CAAC,IAAI,CAAC,EAAE;YAC5C,OAAO,KAAK,CAAA;SACb;QAED;;;WAGG;QACH,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;QAC3B,IAAI,MAAM,KAAK,IAAI;YAAE,OAAO,IAAI,CAAA;QAEhC;;WAEG;QACH,MAAM,MAAM,GAAG,sBAAU,CAAC,IAAI,CAAC,CAAA;QAE/B;;;WAGG;QACH,IAAI,kCAAsB,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,6BAAgB,CAAC,MAAM;YACjF,kCAAsB,CAAC,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,6BAAgB,CAAC,KAAK,EAAE;YACtF,OAAO,IAAI,CAAA;SACZ;QAED;;WAEG;QACH,OAAO,KAAK,CAAA;IACd,CAAC;IAED,QAAQ;QACN;;WAEG;QACH,IAAI,CAAC,GAAG,EAAE,CAAA;QAEV;;;;;WAKG;QACH,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,QAAQ,IAAI,YAAK,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YAC1E,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;SAC3E;QAED;;;;WAIG;QACH,IAAI,YAAK,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACrC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;SACxD;QAED;;;WAGG;QACH,KAAK,MAAM,KAAK,IAAI,mCAAuB,CAAC,IAAI,CAAC,EAAE;YACjD,IAAI,YAAK,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;gBAC3B,CAAC,IAAI,KAAK,CAAC,KAAK,CAAA;aACjB;SACF;QAED;;;;WAIG;QACH,IAAI,YAAK,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YACnC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;SACvD;QAED;;WAEG;QACH,OAAO,CAAC,CAAA;IACV,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,OAAO,CAAC,KAAqB,EAAE,GAAmB;QACvD,MAAM,KAAK,GAAG,IAAI,SAAS,EAAE,CAAA;QAC7B,IAAI,KAAK;YAAE,KAAK,CAAC,MAAM,GAAG,KAAK,CAAA;QAC/B,IAAI,GAAG;YAAE,KAAK,CAAC,IAAI,GAAG,GAAG,CAAA;QACzB,OAAO,KAAK,CAAA;IACd,CAAC;;AA/mBH,8BAgnBC;AA9mBQ,wBAAc,GAAG,CAAC,CAAA;AAClB,sBAAY,GAAG,CAAC,CAAA;AAChB,oBAAU,GAAG,CAAC,CAAA;AACd,sBAAY,GAAG,CAAC,CAAA;AA6mBzB;;GAEG;AACH,iCAAe,CAAC,SAAS,CAAC,SAAS,EAAE,gBAAgB,EAAE,CAAC,CAAC,CAAA;AACzD,iCAAe,CAAC,SAAS,CAAC,SAAS,EAAE,cAAc,EAAE,CAAC,CAAC,CAAA;AACvD,iCAAe,CAAC,SAAS,CAAC,SAAS,EAAE,YAAY,EAAE,CAAC,CAAC,CAAA;AACrD,iCAAe,CAAC,SAAS,CAAC,SAAS,EAAE,cAAc,EAAE,CAAC,CAAC,CAAA"}