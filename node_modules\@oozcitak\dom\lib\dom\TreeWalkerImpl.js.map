{"version": 3, "file": "TreeWalkerImpl.js", "sourceRoot": "", "sources": ["../../src/dom/TreeWalkerImpl.ts"], "names": [], "mappings": ";;AAAA,6CAA6D;AAC7D,mDAA+C;AAC/C,4CAEqB;AAErB;;GAEG;AACH,MAAa,cAAe,SAAQ,6BAAa;IAI/C;;OAEG;IACH,YAAoB,IAAU,EAAE,OAAa;QAC3C,KAAK,CAAC,IAAI,CAAC,CAAA;QAEX,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAA;IACzB,CAAC;IAED,kBAAkB;IAClB,IAAI,WAAW,KAAW,OAAO,IAAI,CAAC,QAAQ,CAAA,CAAC,CAAC;IAChD,IAAI,WAAW,CAAC,KAAW,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAA,CAAC,CAAC;IAEtD,kBAAkB;IAClB,UAAU;QACR;;;WAGG;QACH,IAAI,IAAI,GAAgB,IAAI,CAAC,QAAQ,CAAA;QACrC,OAAO,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK,EAAE;YAC3C;;;;;eAKG;YACH,IAAI,GAAG,IAAI,CAAC,OAAO,CAAA;YACnB,IAAI,IAAI,KAAK,IAAI;gBACf,4BAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,yBAAY,CAAC,MAAM,EAAE;gBACtD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;gBACpB,OAAO,IAAI,CAAA;aACZ;SACF;QAED;;WAEG;QACH,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,UAAU;QACR;;;WAGG;QACH,OAAO,uCAA2B,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAChD,CAAC;IAED,kBAAkB;IAClB,SAAS;QACP;;;WAGG;QACH,OAAO,uCAA2B,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;IACjD,CAAC;IAED,kBAAkB;IAClB,WAAW;QACT;;;WAGG;QACH,OAAO,uCAA2B,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAChD,CAAC;IAED,kBAAkB;IAClB,YAAY;QACV;;;WAGG;QACH,IAAI,IAAI,GAAgB,IAAI,CAAC,QAAQ,CAAA;QAErC,OAAO,IAAI,KAAK,IAAI,CAAC,KAAK,EAAE;YAC1B;;;eAGG;YACH,IAAI,OAAO,GAAgB,IAAI,CAAC,gBAAgB,CAAA;YAChD,OAAO,OAAO,EAAE;gBACd;;;;mBAIG;gBACH,IAAI,GAAG,OAAO,CAAA;gBACd,IAAI,MAAM,GAAG,4BAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;gBAEzC;;mBAEG;gBACH,OAAO,MAAM,KAAK,yBAAY,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,EAAE;oBACxD;;;;uBAIG;oBACH,IAAI,GAAG,IAAI,CAAC,UAAU,CAAA;oBACtB,MAAM,GAAG,4BAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;iBACtC;gBAED;;;mBAGG;gBACH,IAAI,MAAM,KAAK,yBAAY,CAAC,MAAM,EAAE;oBAClC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;oBACpB,OAAO,IAAI,CAAA;iBACZ;gBAED;;mBAEG;gBACH,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAA;aAChC;YAED;;;eAGG;YACH,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE;gBAChD,OAAO,IAAI,CAAA;aACZ;YAED;;eAEG;YACH,IAAI,GAAG,IAAI,CAAC,OAAO,CAAA;YAEnB;;;;eAIG;YACH,IAAI,4BAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,yBAAY,CAAC,MAAM,EAAE;gBACxD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;gBACpB,OAAO,IAAI,CAAA;aACZ;SACF;QAED;;WAEG;QACH,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,eAAe;QACb;;;WAGG;QACH,OAAO,uCAA2B,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;IACjD,CAAC;IAED,kBAAkB;IAClB,QAAQ;QACN;;;;WAIG;QACH,IAAI,IAAI,GAAgB,IAAI,CAAC,QAAQ,CAAA;QACrC,IAAI,MAAM,GAAG,yBAAY,CAAC,MAAM,CAAA;QAEhC,OAAO,IAAI,EAAE;YACX;;eAEG;YACH,OAAO,MAAM,KAAK,yBAAY,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;gBACzD;;;;;;mBAMG;gBACH,IAAI,GAAG,IAAI,CAAC,WAAW,CAAA;gBACvB,MAAM,GAAG,4BAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;gBACrC,IAAI,MAAM,KAAK,yBAAY,CAAC,MAAM,EAAE;oBAClC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;oBACpB,OAAO,IAAI,CAAA;iBACZ;aACF;YAED;;;;eAIG;YACH,IAAI,OAAO,GAAgB,IAAI,CAAA;YAC/B,IAAI,SAAS,GAAgB,IAAI,CAAA;YACjC,OAAO,SAAS,KAAK,IAAI,EAAE;gBACzB;;mBAEG;gBACH,IAAI,SAAS,KAAK,IAAI,CAAC,KAAK,EAAE;oBAC5B,OAAO,IAAI,CAAA;iBACZ;gBACD;;;mBAGG;gBACH,OAAO,GAAG,SAAS,CAAC,YAAY,CAAA;gBAChC,IAAI,OAAO,KAAK,IAAI,EAAE;oBACpB,IAAI,GAAG,OAAO,CAAA;oBACd,MAAK;iBACN;gBACD;;mBAEG;gBACH,SAAS,GAAG,SAAS,CAAC,OAAO,CAAA;aAC9B;YAED;;;;eAIG;YACH,MAAM,GAAG,4BAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;YACrC,IAAI,MAAM,KAAK,yBAAY,CAAC,MAAM,EAAE;gBAClC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;gBACpB,OAAO,IAAI,CAAA;aACZ;SACF;IACH,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,OAAO,CAAC,IAAU,EAAE,OAAa;QACtC,OAAO,IAAI,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IAC1C,CAAC;CAEF;AApPD,wCAoPC"}