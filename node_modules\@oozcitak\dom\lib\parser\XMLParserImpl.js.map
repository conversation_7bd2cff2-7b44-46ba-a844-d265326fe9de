{"version": 3, "file": "XMLParserImpl.js", "sourceRoot": "", "sources": ["../../src/parser/XMLParserImpl.ts"], "names": [], "mappings": ";;AAAA,qDAAiD;AACjD,6CAA0D;AAM1D,2CAA6D;AAC7D,4CAGqB;AACrB,6DAAyD;AAEzD;;;;GAIG;AACH,MAAa,aAAa;IAExB;;;;OAIG;IACH,KAAK,CAAC,MAAc;QAClB,MAAM,KAAK,GAAG,IAAI,+BAAc,CAAC,MAAM,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,CAAC,CAAA;QAE1E,MAAM,GAAG,GAAG,2BAAe,EAAE,CAAA;QAE7B,IAAI,OAAO,GAAS,GAAG,CAAA;QACvB,IAAI,KAAK,GAAG,KAAK,CAAC,SAAS,EAAE,CAAA;QAC7B,OAAO,KAAK,CAAC,IAAI,KAAK,sBAAS,CAAC,GAAG,EAAE;YACnC,QAAQ,KAAK,CAAC,IAAI,EAAE;gBAClB,KAAK,sBAAS,CAAC,WAAW;oBACxB,MAAM,WAAW,GAAqB,KAAK,CAAA;oBAC3C,IAAI,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;wBACjC,MAAM,IAAI,KAAK,CAAC,uBAAuB,GAAG,WAAW,CAAC,OAAO,CAAC,CAAA;qBAC/D;oBACD,MAAK;gBACP,KAAK,sBAAS,CAAC,OAAO;oBACpB,MAAM,OAAO,GAAiB,KAAK,CAAA;oBACnC,IAAI,CAAC,2BAAe,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;wBACnC,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAA;qBACjF;oBACD,IAAI,CAAC,2BAAe,CAAC,OAAO,CAAC,KAAK,CAAC;wBACjC,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;wBAC1E,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAA;qBAC1E;oBACD,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC,kBAAkB,CACvD,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAA;oBAC9C,MAAK;gBACP,KAAK,sBAAS,CAAC,KAAK;oBAClB,MAAM,KAAK,GAAe,KAAK,CAAA;oBAC/B,IAAI,CAAC,2BAAe,CAAC,KAAK,CAAC,IAAI,CAAC;wBAC9B,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;wBAClC,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA;qBACtD;oBACD,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;oBACvD,MAAK;gBACP,KAAK,sBAAS,CAAC,OAAO;oBACpB,MAAM,OAAO,GAAiB,KAAK,CAAA;oBACnC,IAAI,CAAC,2BAAe,CAAC,OAAO,CAAC,IAAI,CAAC;wBAChC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;wBACjE,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAA;qBAC7D;oBACD,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAA;oBACpD,MAAK;gBACP,KAAK,sBAAS,CAAC,EAAE;oBACf,MAAM,EAAE,GAAY,KAAK,CAAA;oBACzB,IAAI,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE;wBAC/D,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAA;qBAC9E;oBACD,IAAI,CAAC,2BAAe,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;wBAC7D,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAA;qBAC5E;oBACD,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,2BAA2B,CACjD,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAA;oBACtB,MAAK;gBACP,KAAK,sBAAS,CAAC,IAAI;oBACjB,MAAM,IAAI,GAAc,KAAK,CAAA;oBAC7B,IAAI,CAAC,2BAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;wBAC/B,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAA;qBAC1D;oBACD,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;oBAClD,MAAK;gBACP,KAAK,sBAAS,CAAC,OAAO;oBACpB,MAAM,OAAO,GAAiB,KAAK,CAAA;oBAEnC,gCAAgC;oBAChC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,kCAAsB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;oBAChE,IAAI,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,sBAAU,CAAC,SAAS,CAAC,EAAE;wBAC3D,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAA;qBAChE;oBACD,IAAI,MAAM,KAAK,OAAO,EAAE;wBACtB,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;qBAC9D;oBACD,IAAI,SAAS,GAAG,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAA;oBAElD,yDAAyD;oBACzD,YAAY;oBACZ,+CAA+C;oBAC/C,MAAM,cAAc,GAA8B,EAAE,CAAA;oBACpD,KAAK,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,OAAO,CAAC,UAAU,EAAE;wBACpD,IAAI,OAAO,KAAK,OAAO,EAAE;4BACvB,SAAS,GAAG,QAAQ,CAAA;yBACrB;6BAAM;4BACL,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,kCAAsB,CAAC,OAAO,CAAC,CAAA;4BACjE,IAAI,SAAS,KAAK,OAAO,EAAE;gCACzB,IAAI,YAAY,KAAK,MAAM,EAAE;oCAC3B,SAAS,GAAG,QAAQ,CAAA;iCACrB;gCACD,cAAc,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAA;6BACxC;yBACF;qBACF;oBAED,8BAA8B;oBAC9B,MAAM,WAAW,GAAG,CAAC,SAAS,KAAK,IAAI,CAAC,CAAC;wBACvC,GAAG,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;wBAC9C,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAA;oBAElC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;oBAEhC,oBAAoB;oBACpB,MAAM,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAA;oBAEvC,KAAK,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,OAAO,CAAC,UAAU,EAAE;wBACpD,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,kCAAsB,CAAC,OAAO,CAAC,CAAA;wBACjE,IAAI,YAAY,GAAkB,IAAI,CAAA;wBACtC,IAAI,SAAS,KAAK,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,IAAI,YAAY,KAAK,OAAO,CAAC,EAAE;4BAC7E,kCAAkC;4BAClC,YAAY,GAAG,iBAAc,CAAC,KAAK,CAAA;yBACpC;6BAAM;4BACL,YAAY,GAAG,WAAW,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAA;4BACxD,IAAI,YAAY,KAAK,IAAI,IAAI,WAAW,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAE;gCACzE,YAAY,GAAG,IAAI,CAAA;6BACpB;iCAAM,IAAI,YAAY,KAAK,IAAI,IAAI,SAAS,KAAK,IAAI,EAAE;gCACtD,YAAY,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI,IAAI,CAAA;6BACjD;yBACF;wBACD,IAAI,YAAY,CAAC,GAAG,CAAC,YAAY,EAAE,YAAY,CAAC,EAAE;4BAChD,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAA;yBAC1D;wBACD,YAAY,CAAC,GAAG,CAAC,YAAY,EAAE,YAAY,CAAC,CAAA;wBAC5C,IAAI,YAAY,KAAK,iBAAc,CAAC,KAAK,EAAE;4BACzC,IAAI,QAAQ,KAAK,iBAAc,CAAC,KAAK,EAAE;gCACrC,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;6BAChD;yBACF;wBACD,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,sBAAU,CAAC,YAAY,CAAC,EAAE;4BACjE,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAA;yBACrE;wBAED,IAAI,SAAS,KAAK,OAAO,IAAI,QAAQ,KAAK,EAAE,EAAE;4BAC5C,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAA;yBACvD;wBAED,IAAI,YAAY,KAAK,IAAI;4BACvB,WAAW,CAAC,cAAc,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAA;;4BAE3D,WAAW,CAAC,YAAY,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;qBAC9C;oBAED,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;wBACxB,OAAO,GAAS,WAAW,CAAA;qBAC5B;oBACD,MAAK;gBACP,KAAK,sBAAS,CAAC,UAAU;oBACvB,MAAM,UAAU,GAAoB,KAAK,CAAA;oBACzC,IAAI,UAAU,CAAC,IAAI,KAAK,OAAO,CAAC,QAAQ,EAAE;wBACxC,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAA;qBACrE;oBACD,0BAA0B;oBAC1B,IAAI,OAAO,CAAC,OAAO,EAAE;wBACnB,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;qBAC1B;oBACD,MAAK;aACR;YAED,KAAK,GAAG,KAAK,CAAC,SAAS,EAAE,CAAA;SAC1B;QACD,OAAO,GAAG,CAAA;IACZ,CAAC;CACF;AAtKD,sCAsKC"}