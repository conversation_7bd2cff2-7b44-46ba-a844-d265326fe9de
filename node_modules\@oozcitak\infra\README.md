# infra
A Javascript implementation of the [Infra Living Standard](https://infra.spec.whatwg.org/).

[![License](http://img.shields.io/npm/l/@oozcitak/infra.svg?style=flat-square)](http://opensource.org/licenses/MIT)
[![NPM Version](http://img.shields.io/npm/v/@oozcitak/infra.svg?style=flat-square)](https://www.npmjs.com/package/@oozcitak/infra)

[![Travis Build Status](http://img.shields.io/travis/oozcitak/infra.svg?style=flat-square)](http://travis-ci.org/oozcitak/infra)
[![AppVeyor Build status](https://ci.appveyor.com/api/projects/status/eq3de0oihhp9p5h2?svg=true)](https://ci.appveyor.com/project/oozcitak/infra)
[![Dev Dependency Status](http://img.shields.io/david/dev/oozcitak/infra.svg?style=flat-square)](https://david-dm.org/oozcitak/infra)
[![Code Coverage](https://img.shields.io/codecov/c/github/oozcitak/infra?style=flat-square)](https://codecov.io/gh/oozcitak/infra)

# version
Current version implements the standard as of commit [d4a3e90](https://infra.spec.whatwg.org/commit-snapshots/d4a3e9003e6695e3e1379a8581d945759566bfac/).
