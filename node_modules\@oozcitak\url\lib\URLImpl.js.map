{"version": 3, "file": "URLImpl.js", "sourceRoot": "", "sources": ["../src/URLImpl.ts"], "names": [], "mappings": ";;AAAA,+DAA2D;AAC3D,6CAA2E;AAC3E,iDAIuB;AAEvB;;GAEG;AACH,MAAa,OAAO;IAKlB;;;;;OAKG;IACH,YAAY,GAAW,EAAE,OAAgB;QACvC;;;;;WAKG;QACH,IAAI,UAAU,GAAqB,IAAI,CAAA;QACvC,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,UAAU,GAAG,6BAAc,CAAC,OAAO,CAAC,CAAA;YACpC,IAAI,UAAU,KAAK,IAAI,EAAE;gBACvB,MAAM,IAAI,SAAS,CAAC,sBAAsB,OAAO,IAAI,CAAC,CAAA;aACvD;SACF;QAED;;;;WAIG;QACH,MAAM,SAAS,GAAG,6BAAc,CAAC,GAAG,EAAE,UAAU,CAAC,CAAA;QACjD,IAAI,SAAS,KAAK,IAAI,EAAE;YACtB,MAAM,IAAI,SAAS,CAAC,iBAAiB,GAAG,IAAI,CAAC,CAAA;SAC9C;QAED;;;;;;;;WAQG;QACH,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,IAAI,EAAE,CAAA;QACnC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAA;QACrB,IAAI,CAAC,YAAY,GAAG,IAAI,yCAAmB,CAAC,KAAK,CAAC,CAAA;QAClD,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI,CAAA;IACrC,CAAC;IAED,kBAAkB;IAClB,IAAI,IAAI;QACN;;;WAGG;QACH,OAAO,4BAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACjC,CAAC;IACD,IAAI,IAAI,CAAC,KAAa;QACpB;;;;WAIG;QACH,MAAM,SAAS,GAAG,6BAAc,CAAC,KAAK,CAAC,CAAA;QACvC,IAAI,SAAS,KAAK,IAAI,EAAE;YACtB,MAAM,IAAI,SAAS,CAAC,iBAAiB,KAAK,IAAI,CAAC,CAAA;SAChD;QACD;;;;;;WAMG;QACH,IAAI,CAAC,IAAI,GAAG,SAAS,CAAA;QACrB,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,EAAE,CAAA;QAC5B,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;QAC7B,IAAI,KAAK,KAAK,IAAI,EAAE;YAClB,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,qCAAsB,CAAC,KAAK,CAAC,CAAA;SACxD;IACH,CAAC;IAED,kBAAkB;IAClB,IAAI,MAAM;QACR;;;WAGG;QACH,OAAO,2CAA4B,CAAC,qBAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;IACxD,CAAC;IAED,kBAAkB;IAClB,IAAI,QAAQ;QACV;;;WAGG;QACH,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAA;IAC/B,CAAC;IACD,IAAI,QAAQ,CAAC,GAAW;QACtB;;;;WAIG;QACH,6BAAc,CAAC,GAAG,GAAG,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,EACvD,wBAAW,CAAC,WAAW,CAAC,CAAA;IAC5B,CAAC;IAED,kBAAkB;IAClB,IAAI,QAAQ;QACV;;;WAGG;QACH,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAA;IAC3B,CAAC;IACD,IAAI,QAAQ,CAAC,GAAW;QACtB;;;;WAIG;QACH,IAAI,8CAA+B,CAAC,IAAI,CAAC,IAAI,CAAC;YAAE,OAAM;QACtD,6BAAc,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IAChC,CAAC;IAED,kBAAkB;IAClB,IAAI,QAAQ;QACV;;;WAGG;QACH,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAA;IAC3B,CAAC;IACD,IAAI,QAAQ,CAAC,GAAW;QACtB;;;;WAIG;QACH,IAAI,8CAA+B,CAAC,IAAI,CAAC,IAAI,CAAC;YAAE,OAAM;QACtD,6BAAc,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IAChC,CAAC;IAED,kBAAkB;IAClB,IAAI,IAAI;QACN;;;;;;WAMG;QACH,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;YAC3B,OAAO,EAAE,CAAA;SACV;aAAM,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;YAClC,OAAO,6BAAc,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SACtC;aAAM;YACL,OAAO,6BAAc,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAA;SACxE;IACH,CAAC;IACD,IAAI,IAAI,CAAC,GAAW;QAClB;;;;;WAKG;QACH,IAAI,IAAI,CAAC,IAAI,CAAC,qBAAqB;YAAE,OAAM;QAC3C,6BAAc,CAAC,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,EACjD,wBAAW,CAAC,IAAI,CAAC,CAAA;IACrB,CAAC;IAED,kBAAkB;IAClB,IAAI,QAAQ;QACV;;;WAGG;QACH,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI;YAAE,OAAO,EAAE,CAAA;QACtC,OAAO,6BAAc,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACvC,CAAC;IACD,IAAI,QAAQ,CAAC,GAAW;QACtB;;;;;WAKG;QACH,IAAI,IAAI,CAAC,IAAI,CAAC,qBAAqB;YAAE,OAAM;QAC3C,6BAAc,CAAC,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,EACjD,wBAAW,CAAC,QAAQ,CAAC,CAAA;IACzB,CAAC;IAED,kBAAkB;IAClB,IAAI,IAAI;QACN;;;WAGG;QACH,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI;YAAE,OAAO,EAAE,CAAA;QACtC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAA;IAClC,CAAC;IACD,IAAI,IAAI,CAAC,GAAW;QAClB;;;;;;;WAOG;QACH,IAAI,8CAA+B,CAAC,IAAI,CAAC,IAAI,CAAC;YAAE,OAAM;QACtD,IAAI,GAAG,KAAK,EAAE,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;SACtB;aAAM;YACL,6BAAc,CAAC,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,EACjD,wBAAW,CAAC,IAAI,CAAC,CAAA;SACpB;IACH,CAAC;IAED,kBAAkB;IAClB,IAAI,QAAQ;QACV;;;;;;;WAOG;QACH,IAAI,IAAI,CAAC,IAAI,CAAC,qBAAqB;YAAE,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAC7D,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,EAAE,CAAA;QAC1C,OAAO,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IACvC,CAAC;IACD,IAAI,QAAQ,CAAC,GAAW;QACtB;;;;;WAKG;QACH,IAAI,IAAI,CAAC,IAAI,CAAC,qBAAqB;YAAE,OAAM;QAC3C,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAA;QACnB,6BAAc,CAAC,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,EACjD,wBAAW,CAAC,SAAS,CAAC,CAAA;IAC1B,CAAC;IAED,kBAAkB;IAClB,IAAI,MAAM;QACR;;;;WAIG;QACH,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE;YAAE,OAAO,EAAE,CAAA;QACjE,OAAO,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;IAC9B,CAAC;IACD,IAAI,MAAM,CAAC,GAAW;QACpB;;;;;;;;;;;WAWG;QACH,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAA;QACrB,IAAI,GAAG,KAAK,EAAE,EAAE;YACd,GAAG,CAAC,KAAK,GAAG,IAAI,CAAA;YAChB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA;YAClC,OAAM;SACP;QACD,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC;YAAE,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QAC5C,GAAG,CAAC,KAAK,GAAG,EAAE,CAAA;QACd,6BAAc,CAAC,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,wBAAW,CAAC,KAAK,CAAC,CAAA;QACjE,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,qCAAsB,CAAC,GAAG,CAAC,CAAA;IACvD,CAAC;IAED,kBAAkB;IAClB,IAAI,YAAY,KAAsB,OAAO,IAAI,CAAC,YAAY,CAAA,CAAC,CAAC;IAEhE,kBAAkB;IAClB,IAAI,IAAI;QACN;;;;WAIG;QACH,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,EAAE;YAAE,OAAO,EAAE,CAAA;QACvE,OAAO,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAA;IACjC,CAAC;IACD,IAAI,IAAI,CAAC,GAAW;QAClB;;;;;;;;WAQG;QACH,IAAI,GAAG,KAAK,EAAE,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;YACzB,OAAM;SACP;QACD,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC;YAAE,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QAC5C,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;QACvB,6BAAc,CAAC,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,EACjD,wBAAW,CAAC,QAAQ,CAAC,CAAA;IACzB,CAAC;IAGD,kBAAkB;IAClB,MAAM,KAAa,OAAO,4BAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,CAAC,CAAC;IAEpD,kBAAkB;IAClB,QAAQ;QACN,OAAO,IAAI,CAAC,IAAI,CAAA;IAClB,CAAC;CAEF;AAxUD,0BAwUC"}