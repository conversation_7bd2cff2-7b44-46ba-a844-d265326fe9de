{"version": 3, "file": "URLSearchParamsImpl.js", "sourceRoot": "", "sources": ["../src/URLSearchParamsImpl.ts"], "names": [], "mappings": ";;AACA,yCAAkD;AAClD,iDAA6E;AAE7E;;GAEG;AACH,MAAa,mBAAmB;IAK9B;;;;OAIG;IACH,YAAY,OAAwD,EAAE;QARtE,UAAK,GAAuB,EAAE,CAAA;QAC9B,eAAU,GAAe,IAAI,CAAA;QAQ3B;;;;;;;;;;;;WAYG;QACH,IAAI,cAAO,CAAC,IAAI,CAAC,EAAE;YACjB,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE;gBACvB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;oBACrB,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAA;iBAC9D;gBACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;aACpC;SACF;aAAM,IAAI,eAAQ,CAAC,IAAI,CAAC,EAAE;YACzB,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE;gBACvB,0BAA0B;gBAC1B,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;oBAC7B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;iBACpC;aACF;SACF;aAAM;YACL,IAAI,CAAC,KAAK,GAAG,qCAAsB,CAAC,IAAI,CAAC,CAAA;SAC1C;IACH,CAAC;IAED;;OAEG;IACH,YAAY;QACV;;;;WAIG;QACH,IAAI,KAAK,GAAkB,mCAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAC3D,IAAI,KAAK,KAAK,EAAE;YAAE,KAAK,GAAG,IAAI,CAAA;QAC9B,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI;YAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;IAClE,CAAC;IAED,kBAAkB;IAClB,MAAM,CAAC,IAAY,EAAE,KAAa;QAChC;;;;WAIG;QACH,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAA;QAC9B,IAAI,CAAC,YAAY,EAAE,CAAA;IACrB,CAAC;IAED,kBAAkB;IAClB,MAAM,CAAC,IAAY;QACjB;;;WAGG;QACH,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC9C,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI;gBAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;SACvD;QACD,IAAI,CAAC,YAAY,EAAE,CAAA;IACrB,CAAC;IAED,kBAAkB;IAClB,GAAG,CAAC,IAAY;QACd;;;;WAIG;QACH,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;YAC7B,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI;gBAAE,OAAO,IAAI,CAAC,CAAC,CAAC,CAAA;SACrC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,MAAM,CAAC,IAAY;QACjB;;;;WAIG;QACH,MAAM,MAAM,GAAa,EAAE,CAAA;QAC3B,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;YAC7B,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI;gBAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;SAC3C;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAED,kBAAkB;IAClB,GAAG,CAAC,IAAY;QACd;;;WAGG;QACH,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;YAC7B,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI;gBAAE,OAAO,IAAI,CAAA;SAClC;QACD,OAAO,KAAK,CAAA;IACd,CAAC;IAED,kBAAkB;IAClB,GAAG,CAAC,IAAY,EAAE,KAAa;QAC7B;;;;;;;WAOG;QACH,MAAM,QAAQ,GAAa,EAAE,CAAA;QAC7B,IAAI,KAAK,GAAG,KAAK,CAAA;QACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC1C,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;gBAC7B,IAAI,CAAC,KAAK,EAAE;oBACV,KAAK,GAAG,IAAI,CAAA;oBACZ,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAA;iBACzB;qBAAM;oBACL,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;iBACjB;aACF;SACF;QACD,IAAI,CAAC,KAAK,EAAE;YACV,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAA;SAC/B;QACD,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE;YACxB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;SACxB;IACH,CAAC;IAED,kBAAkB;IAClB,IAAI;QACF;;;;;WAKG;QACH,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACnE,IAAI,CAAC,YAAY,EAAE,CAAA;IACrB,CAAC;IAED,kBAAkB;IAClB,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;QAChB;;;WAGG;QACH,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;YAC7B,MAAM,IAAI,CAAA;SACX;IACH,CAAC;IAED,kBAAkB;IAClB,QAAQ;QACN,OAAO,mCAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACzC,CAAC;CAEF;AAjLD,kDAiLC"}