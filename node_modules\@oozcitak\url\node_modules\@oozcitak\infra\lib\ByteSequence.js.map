{"version": 3, "file": "ByteSequence.js", "sourceRoot": "", "sources": ["../src/ByteSequence.ts"], "names": [], "mappings": ";;AAAA;;;;GAIG;AACH,SAAgB,MAAM,CAAC,IAAgB;IACrC;;OAEG;IACH,OAAO,IAAI,CAAC,MAAM,CAAA;AACpB,CAAC;AALD,wBAKC;AAED;;;;GAIG;AACH,SAAgB,aAAa,CAAC,IAAgB;IAC5C;;;OAGG;IACH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;QACjB,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE;YAC1B,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;SACnB;KACF;AACH,CAAC;AAXD,sCAWC;AAED;;;;GAIG;AACH,SAAgB,aAAa,CAAC,IAAgB;IAC5C;;;OAGG;IACH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;QACjB,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE;YAC1B,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;SACnB;KACF;AACH,CAAC;AAXD,sCAWC;AAED;;;;;GAKG;AACH,SAAgB,wBAAwB,CAAC,KAAiB,EACxD,KAAiB;IACjB;;;OAGG;IACH,IAAI,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM;QAAE,OAAO,KAAK,CAAA;IAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;QAChB,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;QAChB,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI;YAAE,CAAC,IAAI,IAAI,CAAA;QACrC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI;YAAE,CAAC,IAAI,IAAI,CAAA;QACrC,IAAI,CAAC,KAAK,CAAC;YAAE,OAAO,KAAK,CAAA;KAC1B;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AAfD,4DAeC;AAED;;;;;GAKG;AACH,SAAgB,UAAU,CAAC,KAAiB,EAAE,KAAiB;IAC7D;;;;;;;;OAQG;IACH,IAAI,CAAC,GAAG,CAAC,CAAA;IACT,OAAO,IAAI,EAAE;QACX,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM;YAAE,OAAO,KAAK,CAAA;QACnC,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM;YAAE,OAAO,IAAI,CAAA;QAClC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;YAAE,OAAO,KAAK,CAAA;QACvC,CAAC,EAAE,CAAA;KACJ;AACH,CAAC;AAjBD,gCAiBC;AAED;;;;;GAKG;AACH,SAAgB,YAAY,CAAC,KAAiB,EAAE,KAAiB;IAC/D;;;;;;;;OAQG;IACH,IAAI,CAAC,GAAG,CAAC,CAAA;IACT,OAAO,IAAI,EAAE;QACX,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM;YAAE,OAAO,KAAK,CAAA;QACnC,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM;YAAE,OAAO,IAAI,CAAA;QAClC,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;QAClB,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;QAClB,IAAI,CAAC,GAAG,CAAC;YACP,OAAO,IAAI,CAAA;aACR,IAAI,CAAC,GAAG,CAAC;YACZ,OAAO,KAAK,CAAA;QACd,CAAC,EAAE,CAAA;KACJ;AACH,CAAC;AAtBD,oCAsBC;AAED;;;;GAIG;AACH,SAAgB,gBAAgB,CAAC,IAAgB;IAC/C;;;;OAIG;IACH,OAAO,MAAM,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,CAAA;AACtC,CAAC;AAPD,4CAOC"}