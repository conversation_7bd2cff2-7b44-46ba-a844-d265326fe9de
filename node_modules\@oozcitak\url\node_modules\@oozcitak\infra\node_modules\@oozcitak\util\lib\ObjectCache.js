"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * Represents an object cache with a size limit.
 */
class ObjectCache {
    /**
     * Initializes a new instance of `ObjectCache`.
     *
     * @param limit - maximum number of items to keep in the cache. When the limit
     * is exceeded the first item is removed from the cache.
     */
    constructor(limit = 1000) {
        this._items = new Set();
        this._limit = limit;
    }
    /**
     * Adds a new item to the cache.
     *
     * @param item - an item
     */
    add(item) {
        this._items.add(item);
        if (this._items.size > this._limit) {
            const it = this._items.values().next();
            /* istanbul ignore else */
            if (!it.done) {
                this._items.delete(it.value);
            }
        }
    }
    /**
     * Removes an item from the cache.
     *
     * @param item - an item
     */
    remove(item) {
        this._items.delete(item);
    }
    /**
     * Removes all items from the cache.
     */
    clear() {
        this._items.clear();
    }
    /**
     * Gets the number of items in the cache.
     */
    get length() { return this._items.size; }
    /**
     * Iterates through the items in the cache.
     */
    *entries() {
        yield* this;
    }
    /** @inheritdoc */
    *[Symbol.iterator]() {
        for (const item of this._items) {
            yield item;
        }
    }
}
exports.ObjectCache = ObjectCache;
//# sourceMappingURL=ObjectCache.js.map