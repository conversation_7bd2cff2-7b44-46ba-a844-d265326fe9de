/**
 * Represents an object cache with a size limit.
 */
export declare class ObjectCache<T> {
    private _limit;
    private _items;
    /**
     * Initializes a new instance of `ObjectCache`.
     *
     * @param limit - maximum number of items to keep in the cache. When the limit
     * is exceeded the first item is removed from the cache.
     */
    constructor(limit?: number);
    /**
     * Adds a new item to the cache.
     *
     * @param item - an item
     */
    add(item: T): void;
    /**
     * Removes an item from the cache.
     *
     * @param item - an item
     */
    remove(item: T): void;
    /**
     * Removes all items from the cache.
     */
    clear(): void;
    /**
     * Gets the number of items in the cache.
     */
    get length(): number;
    /**
     * Iterates through the items in the cache.
     */
    entries(): IterableIterator<T>;
    /**
     * Iterates through the items in the cache.
     */
    [Symbol.iterator](): IterableIterator<T>;
}
