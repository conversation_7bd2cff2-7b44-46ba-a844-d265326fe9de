{"version": 3, "file": "StringWalker.js", "sourceRoot": "", "sources": ["../src/StringWalker.ts"], "names": [], "mappings": ";;AAAA;;GAEG;AACH,MAAa,YAAY;IAUvB;;;;OAIG;IACH,YAAY,KAAa;QAXjB,aAAQ,GAAW,CAAC,CAAA;QAY1B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAC/B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,GAAG,KAAc,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAA,CAAC,CAAC;IAE3D;;OAEG;IACH,IAAI,MAAM,KAAa,OAAO,IAAI,CAAC,OAAO,CAAA,CAAC,CAAC;IAE5C;;;OAGG;IACH,SAAS;QACP,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE;YACjC,IAAI,IAAI,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAA;aACrB;iBAAM;gBACL,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;gBACpD,0BAA0B;gBAC1B,IAAI,EAAE,KAAK,SAAS,EAAE;oBACpB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAA;iBACrB;qBAAM;oBACL,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAA;iBACrB;aACF;SACF;QACD,OAAO,IAAI,CAAC,UAAU,CAAA;IACxB,CAAC;IAED;;;OAGG;IACH,CAAC;QACC,IAAI,IAAI,CAAC,EAAE,KAAK,SAAS,EAAE;YACzB,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAA;SACvD;QACD,OAAO,IAAI,CAAC,EAAE,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,SAAS;QACP,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE;YACjC,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC3B,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;SACtD;QACD,OAAO,IAAI,CAAC,UAAU,CAAA;IACxB,CAAC;IAED;;OAEG;IACH,SAAS;QACP,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE;YACjC,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC3B,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;SAClD;QACD,OAAO,IAAI,CAAC,UAAU,CAAA;IACxB,CAAC;IAED;;OAEG;IACH,IAAI,OAAO,KAAc,OAAO,IAAI,CAAC,QAAQ,CAAA,CAAC,CAAC;IAC/C,IAAI,OAAO,CAAC,GAAW;QACrB,IAAI,GAAG,KAAK,IAAI,CAAC,QAAQ;YAAE,OAAM;QAEjC,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAA;QAEnB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAA;QAC3B,IAAI,CAAC,EAAE,GAAG,SAAS,CAAA;QACnB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAA;QAC3B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAA;IAC7B,CAAC;CACF;AAlGD,oCAkGC"}