{"name": "@oozcitak/util", "version": "1.0.2", "keywords": ["util", "js"], "homepage": "http://github.com/oozcitak/util", "description": "Utility functions", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "contributors": [], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/oozcitak/util.git"}, "bugs": {"url": "http://github.com/oozcitak/util/issues"}, "main": "./lib/index", "engines": {"node": ">=6.0"}, "files": ["lib/**/*"], "dependencies": {}, "devDependencies": {"typescript": "*", "jest": "*", "ts-jest": "*", "coveralls": "*", "@types/node": "*", "@types/jest": "*"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{ts,tsx}"]}, "scripts": {"compile": "rm -rf lib && tsc", "test": "npm run compile && jest --coverage", "publish-public": "npm run test && npm publish --access public"}}