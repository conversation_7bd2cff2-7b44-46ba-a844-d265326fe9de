"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * Represents an object with lazy initialization.
 */
class Lazy {
    /**
     * Initializes a new instance of `Lazy`.
     *
     * @param initFunc - initializer function
     */
    constructor(initFunc) {
        this._initialized = false;
        this._value = undefined;
        this._initFunc = initFunc;
    }
    /**
     * Gets the value of the object.
     */
    get value() {
        if (!this._initialized) {
            this._value = this._initFunc();
            this._initialized = true;
        }
        return this._value;
    }
}
exports.Lazy = Lazy;
//# sourceMappingURL=Lazy.js.map