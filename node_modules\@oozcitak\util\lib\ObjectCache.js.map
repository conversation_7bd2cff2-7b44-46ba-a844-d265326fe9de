{"version": 3, "file": "ObjectCache.js", "sourceRoot": "", "sources": ["../src/ObjectCache.ts"], "names": [], "mappings": ";;AAAA;;GAEG;AACH,MAAa,WAAW;IAKtB;;;;;OAKG;IACH,YAAmB,QAAgB,IAAI;QAR/B,WAAM,GAAG,IAAI,GAAG,EAAgB,CAAA;QAStC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;IACrB,CAAC;IAED;;;;OAIG;IACH,GAAG,CAAC,GAAS;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;IAC7B,CAAC;IAED;;;;;OAKG;IACH,GAAG,CAAC,GAAS,EAAE,KAAa;QAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;QAC3B,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE;YAClC,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAA;YACpC,0BAA0B;YAC1B,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE;gBACZ,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;aAC7B;SACF;IACH,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,GAAS;QACd,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;IAChC,CAAC;IAED;;;;OAIG;IACH,GAAG,CAAC,GAAS;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;IAC7B,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAA;IACrB,CAAC;IAED;;OAEG;IACH,IAAI,IAAI,KAAa,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAA,CAAC,CAAC;IAE9C;;OAEG;IACH,OAAO,CAAC,QAA4C,EAAE,OAAa;QACjE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,CAAC,IAAI;QACH,KAAM,CAAC,CAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;IAC3B,CAAC;IAED;;OAEG;IACH,CAAC,MAAM;QACL,KAAM,CAAC,CAAA,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA;IAC7B,CAAC;IAED;;OAEG;IACH,CAAC,OAAO;QACN,KAAM,CAAC,CAAA,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAA;IAC9B,CAAC;IAED;;OAEG;IACH,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;QAChB,KAAM,CAAC,CAAA,IAAI,CAAC,MAAM,CAAA;IACpB,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;QACtB,OAAO,aAAa,CAAA;IACtB,CAAC;CAEF;AAjHD,kCAiHC"}