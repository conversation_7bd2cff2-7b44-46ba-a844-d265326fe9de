{"name": "error", "version": "4.4.0", "description": "Custom errors", "keywords": [], "author": "Raynos <<EMAIL>>", "repository": "git://github.com/Raynos/error.git", "main": "index", "homepage": "https://github.com/Raynos/error", "contributors": [{"name": "<PERSON><PERSON>"}], "bugs": {"url": "https://github.com/Raynos/error/issues", "email": "<EMAIL>"}, "dependencies": {"camelize": "^1.0.0", "string-template": "~0.2.0", "xtend": "~4.0.0"}, "devDependencies": {"tape": "~3.0.0"}, "licenses": [{"type": "MIT", "url": "http://github.com/Raynos/error/raw/master/LICENSE"}], "scripts": {"test": "node ./test.js", "start": "node ./index.js", "watch": "nodemon -w ./index.js index.js", "travis-test": "istanbul cover ./test/index.js && ((cat coverage/lcov.info | coveralls) || exit 0)", "cover": "istanbul cover --report none --print detail ./test/index.js", "view-cover": "istanbul report html && google-chrome ./coverage/index.html", "test-browser": "testem-browser ./test/browser/index.js", "testem": "testem-both -b=./test/browser/index.js"}, "testling": {"files": "test/index.js", "browsers": ["ie/8..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}}