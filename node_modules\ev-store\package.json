{"name": "ev-store", "version": "7.0.0", "description": "Stores event listeners and event handles on a DOM object", "keywords": [], "author": "Raynos <<EMAIL>>", "repository": "git://github.com/Raynos/ev-store.git", "main": "index", "homepage": "https://github.com/Raynos/ev-store", "contributors": [{"name": "<PERSON><PERSON>"}], "bugs": {"url": "https://github.com/Raynos/ev-store/issues", "email": "<EMAIL>"}, "dependencies": {"individual": "^3.0.0"}, "devDependencies": {"element": "~0.1.4", "global": "^4.3.0", "min-document": "^2.12.0", "run-browser": "^1.3.1", "tap-spec": "^0.1.9", "tape": "~2.12.0"}, "licenses": [{"type": "MIT", "url": "http://github.com/Raynos/ev-store/raw/master/LICENSE"}], "scripts": {"test": "run-browser test.js -b | tap-spec"}}