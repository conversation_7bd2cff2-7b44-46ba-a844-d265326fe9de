{"name": "html-to-docx", "version": "1.8.0", "description": "HTML to DOCX converter", "keywords": ["html", "docx", "html-to-docx", "html to docx", "office", "word"], "main": "dist/html-to-docx.umd.js", "module": "dist/html-to-docx.esm.js", "scripts": {"test": "npm run build && node example/example-node.js", "prerelease": "rollup -c", "release": "standard-version", "lint": "eslint --fix .", "prettier:check": "prettier --check '**/*.{js}'", "validate": "run-s lint prettier:check", "build": "rollup -c", "prepare": "husky install"}, "repository": {"type": "git", "url": "git+https://github.com/privateOmega/html-to-docx.git"}, "author": "privateOmega <<EMAIL>>", "contributors": ["amrita-syn <<EMAIL>>", "charuthaB <<EMAIL>>", "hanagejet", "kuru<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "juralio-james", "nicolasiscoding <<EMAIL>>", "zedtux <<EMAIL>>", "hlerebours", "hakjeri", "tasola"], "license": "MIT", "bugs": {"url": "https://github.com/privateOmega/html-to-docx/issues"}, "homepage": "https://github.com/privateOmega/html-to-docx#readme", "devDependencies": {"@commitlint/cli": "^13.1.0", "@commitlint/config-conventional": "^13.1.0", "@rollup/plugin-commonjs": "^12.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.1.1", "eslint": "^7.32.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-prettier": "^4.0.0", "husky": "^7.0.0", "lint-staged": "^11.1.2", "prettier": "^2.4.1", "rollup": "^2.62.0", "rollup-plugin-cleaner": "^1.0.0", "rollup-plugin-node-builtins": "^2.1.2", "rollup-plugin-terser": "^7.0.2", "standard-version": "^9.3.1"}, "dependencies": {"@oozcitak/util": "8.3.4", "@oozcitak/dom": "1.15.6", "color-name": "^1.1.4", "html-entities": "^2.3.3", "html-to-vdom": "^0.7.0", "image-size": "^1.0.0", "image-to-base64": "^2.2.0", "jszip": "^3.7.1", "lodash": "^4.17.21", "mime-types": "^2.1.35", "nanoid": "^3.1.25", "virtual-dom": "^2.1.1", "xmlbuilder2": "2.1.2"}, "lint-staged": {"src/**/*.js": ["prettier --write", "eslint --fix"]}, "resolutions": {"@oozcitak/util": "8.3.4", "@oozcitak/dom": "1.15.6"}, "overrides": {"@oozcitak/util": "8.3.4", "@oozcitak/dom": "1.15.6"}}