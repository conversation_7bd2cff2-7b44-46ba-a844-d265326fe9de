{"name": "html-to-vdom", "version": "0.7.0", "description": "Converts html into a vtree", "main": "index.js", "scripts": {"test": "mocha test", "test-coverage": "NODE_ENV=test ./node_modules/.bin/istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- -R spec && rm -rf ./coverage"}, "repository": {"type": "git", "url": "git://github.com/<PERSON>eyer/html-to-vdom.git"}, "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/<PERSON><PERSON>/html-to-vdom/issues"}, "homepage": "https://github.com/<PERSON><PERSON>/html-to-vdom", "dependencies": {"ent": "^2.0.0", "htmlparser2": "^3.8.2"}, "devDependencies": {"chai": "^1.9.1", "chai-as-promised": "^4.1.1", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "mocha": "^1.21.4", "virtual-dom": "2.0.1"}}