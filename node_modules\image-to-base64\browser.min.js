"use strict";!function(e){function t(e){return window.btoa([].slice.call(new Uint8Array(e)).map(function(e){return String.fromCharCode(e)}).join(""))}function n(e,n){return"fetch"in window&&"Promise"in window?fetch(e,n||{}).then(function(e){return e.arrayBuffer()}).then(t):Promise.reject("[*] It's image2base64 not compatible with your browser.")}"undefined"!=typeof module?module.exports=n:e.imageToBase64=n}(this);
