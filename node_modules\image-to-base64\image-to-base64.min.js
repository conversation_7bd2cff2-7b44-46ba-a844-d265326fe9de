"use strict";function validUrl(e){return/http(s)?:\/\/(\w+:?\w*@)?(\S+)(:\d+)?((?<=\.)\w+)+(\/([\w#!:.?+=&%@!\-/])*)?/gi.test(e)}function validTypeImage(e){return/(?<=\S+)\.(jpg|png|jpeg)/gi.test(e)}function base64ToNode(e){return e.toString("base64")}function readFileAndConvert(e){var r=require("fs"),t=require("path");return r.statSync(e).isFile()?base64ToNode(r.readFileSync(t.resolve(e)).toString("base64")):null}function isImage(e){return validTypeImage(e)?Promise.resolve(readFileAndConvert(e)):Promise.reject("[*] Occurent some error... [validTypeImage] == false")}function imageToBase64(e){return validUrl(e)?require("node-fetch")(e).then(function(e){return e.buffer()}).then(base64ToNode):isImage(e)}module.exports=imageToBase64;
