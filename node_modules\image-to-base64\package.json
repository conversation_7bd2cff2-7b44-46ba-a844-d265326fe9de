{"name": "image-to-base64", "version": "2.2.0", "description": "Generate a image to base64.", "main": "image-to-base64.min.js", "scripts": {"minify:base": "uglifyjs image-to-base64.js -c -m -o image-to-base64.min.js", "minify:browser": "uglifyjs browser.js -c -m -o browser.min.js", "minify": "npm run minify:base && npm run minify:browser", "test": "mocha ./test"}, "repository": {"type": "git", "url": "git+https://github.com/renanbastos93/image-to-base64.git"}, "keywords": ["node", "nodejs", "module convert base64 nodejs", "image2base64", "image-to-base64", "convert-image-base64", "convert", "save", "code", "base64", "image", "webpack", "loader", "img", "src", "img src"], "author": "<PERSON><PERSON> - <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/renanbastos93/image-to-base64/issues"}, "homepage": "https://github.com/renanbastos93/image-to-base64#readme", "dependencies": {"node-fetch": "^2.6.0"}, "devDependencies": {"uglify-js": "^3.9.1", "mocha": "^7.1.1"}}