{"name": "individual", "version": "3.0.0", "description": "G<PERSON><PERSON>eed individual values", "keywords": [], "author": "Raynos <<EMAIL>>", "repository": "git://github.com/Raynos/individual.git", "main": "index", "homepage": "https://github.com/Raynos/individual", "contributors": [{"name": "<PERSON>"}], "bugs": {"url": "https://github.com/Raynos/individual/issues", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"coveralls": "^2.10.0", "istanbul": "^0.2.7", "run-browser": "^1.3.1", "tape": "^2.12.3"}, "licenses": [{"type": "MIT", "url": "http://github.com/Raynos/individual/raw/master/LICENSE"}], "scripts": {"test": "node test.js", "cover": "istanbul cover --report none --print detail test.js", "travis": "npm run cover -s && istanbul report lcov && ((cat coverage/lcov.info | coveralls) || exit 0)"}, "testling": {"files": "test.js", "browsers": ["ie/8..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}}