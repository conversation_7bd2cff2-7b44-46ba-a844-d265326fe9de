{"name": "xmlbuilder2", "version": "2.1.2", "keywords": ["xml", "xmlbuilder"], "homepage": "http://github.com/oozcitak/xmlbuilder2", "description": "An XML builder for node.js", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "contributors": [], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/oozcitak/xmlbuilder2.git"}, "bugs": {"url": "http://github.com/oozcitak/xmlbuilder2/issues"}, "main": "./lib/index", "engines": {"node": ">=8.0"}, "files": ["lib/**/*"], "types": "./lib/index.d.ts", "dependencies": {"@oozcitak/util": "8.3.3", "@oozcitak/dom": "1.15.5", "@oozcitak/infra": "1.0.5"}, "devDependencies": {"@types/benchmark": "*", "@types/dedent": "*", "@types/jest": "*", "@types/libxmljs": "*", "@types/node": "*", "benchmark": "*", "chalk": "*", "dedent": "*", "glob": "*", "jest": "*", "libxmljs": "*", "ts-jest": "*", "ts-node": "*", "typescript": "*", "xmlbuilder": "*", "xpath": "*"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "testRegex": "/test/.*\\.test\\.tsx?$", "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{ts,tsx}"]}, "scripts": {"pretest": "rm -rf ./lib && tsc --version && tsc", "test": "jest --coverage", "perf": "npm run pretest && ts-node ./perf/perf.ts", "perf-cb": "npm run pretest && ts-node ./perf/perf-cb.ts", "prof-serialize": "npm run pretest && rm -f isolate-*-v8.log && node --prof ./perf/prof-serialize.js && find . -name isolate-*-v8.log -exec mv {} isolate-v8.log ; && node --prof-process isolate-v8.log > isolate-serialize.log && rm isolate-v8.log", "postpublish": "git push --all && git push --tags", "servedocs": "(cd docs && bundle exec jekyll serve)"}}